const { Op } = require("sequelize");
const db = require("../_helpers/db");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params = {}) {
  let where = {};

  if (params.userId) {
    where.user_id = params.userId;
  }

  if (params.subscriptionId) {
    where.razorpay_subscription_id = params.subscriptionId;
  }

  return await db.UserSubscription.findAll({
    where,
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.UserSubscription.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper
async function getSingleRecord(id) {
  const record = await db.UserSubscription.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
