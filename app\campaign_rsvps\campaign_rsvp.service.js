const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const campaignService = require("../campaigns/campaign.service");
module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getUpcomingUserEventsForUser,
};

db.CampaignRsvps.belongsTo(db.Campaign, {
  as: "campaignInfo",
  through: "campaigns",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});
db.CampaignRsvps.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

async function getAllOld(params) {
  const where = {};
  const { campaignId, rvspValue, userId } = params;
  if (campaignId) where.campaign_id = campaignId;
  if (rvspValue) where.rsvp_value = rvspValue;
  if (userId) {
    where.user_id = userId;
    // where.rsvp_value = "yes";
  }

  return await db.CampaignRsvps.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname", "gender", "age", "skills"],
      },
    ],
  });
}

async function getAll(params) {
  const where = {};
  const { campaignId, rvspValue, userId, type } = params;

  if (campaignId) where.campaign_id = campaignId;
  if (rvspValue) where.rsvp_value = rvspValue;
  if (userId) {
    where.user_id = userId;
  }

  const results = await db.CampaignRsvps.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: ["id", "name","campaign_start_date","fileName"],
      },
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname", "gender", "age", "skills"],
      },
    ],
    raw: true,
    nest: true,
  });

  const currentDate = new Date();
  let totalImpactHours = 0;

  const enrichedResults = results.map((item) => {
    let statusLabel = "";
    const eventDate = item.campaignInfo?.campaign_start_date
      ? new Date(item.campaignInfo.campaign_start_date)
      : null;

    if (item.type === "Participated") {
        console.log("item",item);
      if (item.rsvp_value === "yes") {
        statusLabel = eventDate && eventDate < currentDate ? "Missed" : "Going";
      } else if (item.rsvp_value === "no") {
        statusLabel = "Cancelled";
      }
    } else if (item.type === "Scanned") {
      statusLabel = "Attended";

      // ✅ Only add impact hours if type === "scanned"
      const impact = parseFloat(item.impactHours) || 0;
      totalImpactHours += impact;
    }

    return {
      ...item,
      statusLabel,
    };
  });

  return {
    data: enrichedResults,
    totalImpactHours,
  };
}


async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // Step 1: Get Campaign Info (required for all cases)
  const campaign = await db.Campaign.findByPk(params?.campaign_id, {
    include: [
      {
        model: db.Category,
        as: "categoryInfo",
      },
    ],
  });
  if (!campaign) throw new Error("Campaign not found");

  // Set actualEventDate from campaign_start_date
  params.actualEventDate = campaign.campaign_start_date;

  // Step 2: Check if RSVP already exists
  const existingRsvp = await db.CampaignRsvps.findOne({
    where: {
      campaign_id: params.campaign_id,
      user_id: params.user_id,
    },
  });

  // Function to calculate actualHours and impactHours
  const calculateHours = () => {
    const categoryRatio = campaign.category?.ratio || 1;

    let actualHours = 0;
    if (campaign.fullday_event === "yes") {
      actualHours = 8;
    } else if (campaign.fullday_event === "no") {
      const startTime = new Date(`1970-01-01T${campaign.event_start_time}Z`);
      let endTime = new Date(`1970-01-01T${campaign.event_end_time}Z`);
      if (endTime <= startTime) {
        endTime.setUTCDate(endTime.getUTCDate() + 1);
      }
      actualHours = (endTime - startTime) / (1000 * 60 * 60);
    }

    const impactHours = Math.round(actualHours / categoryRatio);
    return {
      actualHours: Math.round(actualHours),
      impactHours,
    };
  };

  // Step 3: If RSVP exists and type is being upgraded to 'scanned'
  if (existingRsvp) {
    if (params.type === "Scanned" && existingRsvp.type !== "Scanned") {
      const { actualHours, impactHours } = calculateHours();

      params.impactHours = impactHours;
      params.actualHours = actualHours;

      if (impactHours > 0) {
        const user = await db.User.findByPk(existingRsvp.user_id);
        if (user) {
          user.total_impact_created =
            (user.total_impact_created || 0) + impactHours;
          await user.save();
        }
      }
    }

    Object.assign(existingRsvp, params);
    await existingRsvp.save();
    return existingRsvp.get();
  }

  // Step 4: If new RSVP and type is 'scanned', calculate and assign hours
  if (params.type === "Scanned") {
    const { actualHours, impactHours } = calculateHours();
    params.impactHours = impactHours;
    params.actualHours = actualHours;

    if (impactHours > 0) {
      const user = await db.User.findByPk(params.user_id);
      if (user) {
        user.total_impact_created =
          (user.total_impact_created || 0) + impactHours;
        await user.save();
      }
    }
  }

  // Step 5: Create new RSVP
  const record = await db.CampaignRsvps.create(params);
  return record;
}

async function update(id, params) {
    const record = await getSingleRecord(id);

    // copy params to CampaignRsvps and save
    Object.assign(record, params);
    await record.save();

    return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.CampaignRsvps.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getUpcomingUserEventsForUser(params) {
  const campaigns = await campaignService.getUpcomingCampaigns({
    type: "Event",
  });
  const campaignIds = campaigns.map((c) => c.id);

  const rsvpRecords = await db.CampaignRsvps.findAll({
    where: {
      user_id: params.userId,
      rsvp_value: "yes",
      campaign_id: campaignIds,
    },
    include: [
      {
        model: db.Campaign,
        as: "campaignInfo",
        attributes: [
          "id",
          "name",
          "fileName",
          "address",
          "event_time",
          "event_date",
          "description",
        ],
      },
    ],
  });
  rsvpRecords.sort(
    (a, b) =>
      new Date(a.campaignInfo.event_date) - new Date(b.campaignInfo.event_date)
  );

  return params.isAll === "yes" ? rsvpRecords : rsvpRecords.slice(0, 3);
}
