const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const userSubscriptionService = require("./user-subscription.service");

// routes
router.get("/", getAll);
router.post("/", createSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

// ----- Controller Functions -----

function create(req, res, next) {
  userSubscriptionService
    .create(req.body)
    .then(() => {
      return res.json({
        status: true,
        message: "Subscription created successfully",
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  userSubscriptionService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  userSubscriptionService
    .getById(req.params.id)
    .then((record) => res.json(record))
    .catch(next);
}

function createSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.number().required(),
    razorpay_subscription_id: Joi.string().required(),
    plan_id: Joi.string().required(),
    status: Joi.string().optional(),
  });
  validateRequest(req, next, schema);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.number().required(),
    razorpay_subscription_id: Joi.string().required(),
    plan_id: Joi.string().required(),
    status: Joi.string().optional(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userSubscriptionService
    .update(req.params.id, req.body)
    .then((record) => res.json(record))
    .catch(next);
}

function _delete(req, res, next) {
  userSubscriptionService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Subscription deleted successfully" })
    )
    .catch(next);
}
