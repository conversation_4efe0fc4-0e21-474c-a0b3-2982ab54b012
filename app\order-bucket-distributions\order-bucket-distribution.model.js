const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    order_id: { type: DataTypes.INTEGER, allowNull: false },
    bucket_id: { type: DataTypes.INTEGER, allowNull: false },
    bucket_item_id: { type: DataTypes.INTEGER, allowNull: false },
    transaction_id: { type: DataTypes.INTEGER, allowNull: true },
    ngo_id: { type: DataTypes.INTEGER, allowNull: true },
    campaign_id: { type: DataTypes.INTEGER, allowNull: true },
    category_id: { type: DataTypes.INTEGER, allowNull: true },
    percentage: { type: DataTypes.DECIMAL(10, 2), allowNull: false },
    share_amount: { type: DataTypes.DECIMAL(15, 2), allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("order_bucket_distributions", attributes, options);
}
