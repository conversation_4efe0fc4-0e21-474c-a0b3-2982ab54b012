const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const orderBucketDistributionService = require("./order-bucket-distribution.service");

// routes
router.get("/", getAll);
router.post("/", createSchema, create);
router.get("/:id", getById);
router.put("/:id", createSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  orderBucketDistributionService
    .create(req.body)
    .then((record) => res.json({ status: true, message: "Record created successfully", data: record }))
    .catch(next);
}

function getAll(req, res, next) {
  orderBucketDistributionService
    .getAll(req.query)
    .then((records) => res.json(records))
    .catch(next);
}

function getById(req, res, next) {
  orderBucketDistributionService
    .getById(req.params.id)
    .then((record) => res.json(record))
    .catch(next);
}

function createSchema(req, res, next) {
  const schema = Joi.object({
    order_id: Joi.number().required(),
    bucket_id: Joi.number().required(),
    bucket_item_id: Joi.number().required(),
    transaction_id: Joi.number().allow(null),
    ngo_id: Joi.number().allow(null),
    campaign_id: Joi.number().allow(null),
    category_id: Joi.number().allow(null),
    percentage: Joi.number().required(),
    share_amount: Joi.number().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  orderBucketDistributionService
    .update(req.params.id, req.body)
    .then((record) => res.json(record))
    .catch(next);
}

function _delete(req, res, next) {
  orderBucketDistributionService
    .delete(req.params.id)
    .then(() => res.json({ status: true, message: "Record deleted successfully" }))
    .catch(next);
} 