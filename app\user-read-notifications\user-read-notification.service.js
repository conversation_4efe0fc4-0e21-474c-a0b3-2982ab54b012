const db = require("../_helpers/db");
const { Op } = require("sequelize");


module.exports = {
  create,
  update,
};

// Mark a notification as read by a user
async function create({ user_id, notifications }) {
  if (!user_id || !Array.isArray(notifications) || notifications.length === 0)
    throw new Error("user_id and an array of notifications are required");
  // Upsert: only one read per user-notification
  await Promise.all(
    notifications.map((notification_id) =>
      db.UserReadNotification.findOrCreate({
        where: { user_id, notification_id },
        defaults: { isRead: "no" },
      })
    )
  );

  // Now return the same response as getAll in user-notification.service.js
  // 1. Get unread notifications
  const where = {
    type_id: { [Op.or]: [user_id, null] },
    id: {
      [Op.notIn]: db.sequelize.literal(`(
        SELECT notification_id FROM user_read_notifications WHERE user_id = ${user_id}
      )`)
    }
  };
  const unreadNotifications = await db.UserNotification.findAll({
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name", "description"],
      },
    ],
    where,
  });
  // 2. Get read notifications (full objects)
  const readRows = await db.UserReadNotification.findAll({
    where: { user_id },
    attributes: ["notification_id"]
  });
  const readIds = readRows.map(r => r.notification_id);
  let readNotifications = [];
  if (readIds.length > 0) {
    readNotifications = await db.UserNotification.findAll({
      where: { id: readIds },
      include: [
        {
          model: db.Category,
          as: "categoryInfo",
          attributes: ["id", "name", "description"],
        },
      ],
    });
  }
  return { unreadNotifications, readNotifications };
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function getSingleRecord(id) {
  const record = await db.UserReadNotification.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
