const db = require("../_helpers/db");
const ExcelJS = require("exceljs");
const path = require("path");
const fs = require("fs");
const { Op, QueryTypes, Sequelize } = require("sequelize");

// Database associations are already defined in transaction.service.js
// We'll use the existing aliases in our queries

module.exports = {
  generateCampaignReport,
  generateEventReport,
  generatePlatformRevenueReport,
  generateDonationTransactionLog,
  generateAuditLoggingReport,
  generateCampaignDonorReport,
  generateEventVolunteerReport,
};

// Generate Campaign Reports
async function generateCampaignReport(
  reportType,
  ngoId = null,
  startDate = null,
  endDate = null
) {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    let fileName, reportTitle;

    if (reportType === "campaign_financial_summary") {
      if (ngoId) {
        fileName = `NGO_Campaign_Report_${ngoId}_${timestamp}.xlsx`;
        reportTitle = "My Campaign Snapshot";
      } else {
        fileName = `Admin_Campaign_Financial_Summary_${timestamp}.xlsx`;
        reportTitle = "Campaign Financial Summary";
      }
      await generateCampaignFinancialSummary(
        workbook,
        ngoId,
        reportTitle,
        startDate,
        endDate
      );
    }

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
    };
  } catch (error) {
    console.error("Error generating campaign report:", error);
    throw error;
  }
}

// Generate Event Reports
async function generateEventReport(
  reportType,
  ngoId = null,
  startDate = null,
  endDate = null
) {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    let fileName, reportTitle;

    if (reportType === "event_attendance_summary") {
      if (ngoId) {
        fileName = `NGO_Event_Report_${ngoId}_${timestamp}.xlsx`;
        reportTitle = "My Event Attendance & Impact";
      } else {
        fileName = `Admin_Event_Attendance_Summary_${timestamp}.xlsx`;
        reportTitle = "Event Attendance Summary";
      }
      await generateEventAttendanceSummary(
        workbook,
        ngoId,
        reportTitle,
        startDate,
        endDate
      );
    }

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
    };
  } catch (error) {
    console.error("Error generating event report:", error);
    throw error;
  }
}

// Generate Platform Revenue Report (Admin Only)
async function generatePlatformRevenueReport() {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileName = `Platform_Revenue_Snapshot_${timestamp}.xlsx`;

    await generatePlatformRevenueSummary(workbook);

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
    };
  } catch (error) {
    console.error("Error generating platform revenue report:", error);
    throw error;
  }
}

// Generate Donation Transaction Log (NGO Only)
async function generateDonationTransactionLog(
  ngoId,
  startDate = null,
  endDate = null
) {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileName = `Donation_Transaction_Log_${ngoId}_${timestamp}.xlsx`;

    await generateTransactionLogSheet(workbook, ngoId, startDate, endDate);

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
    };
  } catch (error) {
    console.error("Error generating transaction log:", error);
    throw error;
  }
}

// Helper function to generate Campaign Financial Summary
async function generateCampaignFinancialSummary(
  workbook,
  ngoId,
  reportTitle,
  startDate = null,
  endDate = null
) {
  const worksheet = workbook.addWorksheet("Campaign Financial Summary");

  // Set up headers and styling
  worksheet.mergeCells("A1:H1");
  worksheet.getCell("A1").value = reportTitle;
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Add generation timestamp
  worksheet.mergeCells("A2:H2");
  worksheet.getCell(
    "A2"
  ).value = `Generated on: ${new Date().toLocaleString()}`;
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  // Add NGO info if admin is downloading
  let startRow = 4;
  if (!ngoId) {
    // Add NGO column for admin reports
    worksheet.getRow(startRow).values = [
      "NGO Name",
      "Campaign Name",
      "Category",
      "Gross Raised (₹)",
      "Donor Count",
      "Total Fees (₹)",
      "Net to NGO (₹)",
      "% Target Achieved",
    ];
  } else {
    // NGO-specific report without NGO column
    worksheet.getRow(startRow).values = [
      "Campaign Name",
      "Category",
      "Gross Raised (₹)",
      "Donor Count",
      "Total Fees (₹)",
      "Net to NGO (₹)",
      "% Target Achieved",
    ];
  }

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Get campaign data with date filtering
  const whereCondition = ngoId
    ? { ngo_id: ngoId, fund_raising_target: { [Op.ne]: null } }
    : { fund_raising_target: { [Op.ne]: null } };

  // Add date filtering
  if (startDate) {
    whereCondition.createdAt = { [Op.gte]: new Date(startDate) };
  }
  if (endDate) {
    whereCondition.createdAt = whereCondition.createdAt
      ? {
          ...whereCondition.createdAt,
          [Op.lte]: new Date(endDate + "T23:59:59.999Z"),
        }
      : { [Op.lte]: new Date(endDate + "T23:59:59.999Z") };
  }

  const campaigns = await db.Campaign.findAll({
    where: whereCondition,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["name"],
      },
    ],
    order: [["createdAt", "DESC"]],
  });

  // Add campaign data
  let currentRow = startRow + 1;
  for (const campaign of campaigns) {
    // Get transaction data for this campaign
    const transactionData = await db.sequelize.query(
      `
      SELECT 
        COUNT(DISTINCT t.user_id) as donorCount,
        SUM(t.amount) as grossRaised,
        SUM(t.payment_gateway_charges + t.dr_tip_after_charges + t.user_convenience_after_charges + t.ngo_platform_convenience_fee) as totalFees,
        SUM(t.ngo_net_donation) as netToNgo
      FROM transactions t
      WHERE t.campaign_id = :campaignId 
        AND t.donation_type = 'campaign' 
        AND t.status = 'captured'
    `,
      {
        replacements: { campaignId: campaign.id },
        type: QueryTypes.SELECT,
      }
    );

    const stats = transactionData[0];
    const grossRaised = parseFloat(stats.grossRaised || 0);
    const targetAchieved =
      campaign.fund_raising_target > 0
        ? ((grossRaised / campaign.fund_raising_target) * 100).toFixed(2)
        : 0;

    if (!ngoId) {
      // Admin report with NGO column
      worksheet.getRow(currentRow).values = [
        campaign.ngoInfo?.name || "N/A",
        campaign.name,
        campaign.categoryInfo?.name || "N/A",
        grossRaised,
        parseInt(stats.donorCount || 0),
        parseFloat(stats.totalFees || 0),
        parseFloat(stats.netToNgo || 0),
        `${targetAchieved}%`,
      ];
    } else {
      // NGO report without NGO column
      worksheet.getRow(currentRow).values = [
        campaign.name,
        campaign.categoryInfo?.name || "N/A",
        grossRaised,
        parseInt(stats.donorCount || 0),
        parseFloat(stats.totalFees || 0),
        parseFloat(stats.netToNgo || 0),
        `${targetAchieved}%`,
      ];
    }
    currentRow++;
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 15;
  });

  // Add borders
  const range = ngoId ? "A4:G" + (currentRow - 1) : "A4:H" + (currentRow - 1);
  worksheet.getCell(range).border = {
    top: { style: "thin" },
    left: { style: "thin" },
    bottom: { style: "thin" },
    right: { style: "thin" },
  };
}

// Helper function to generate Event Attendance Summary
async function generateEventAttendanceSummary(
  workbook,
  ngoId,
  reportTitle,
  startDate = null,
  endDate = null
) {
  const worksheet = workbook.addWorksheet("Event Attendance Summary");

  // Set up headers and styling
  worksheet.mergeCells("A1:H1");
  worksheet.getCell("A1").value = reportTitle;
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Add generation timestamp
  worksheet.mergeCells("A2:H2");
  worksheet.getCell(
    "A2"
  ).value = `Generated on: ${new Date().toLocaleString()}`;
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  // Add headers
  let startRow = 4;
  if (!ngoId) {
    // Admin report with NGO column
    worksheet.getRow(startRow).values = [
      "NGO Name",
      "Event Name",
      "Event Type",
      "Event Date",
      "Registrations",
      "Attendance Rate (%)",
      "Volunteer Hours",
      "Impact Numbers",
    ];
  } else {
    // NGO report without NGO column
    worksheet.getRow(startRow).values = [
      "Event Name",
      "Event Type",
      "Event Date",
      "Registrations",
      "Attendance Rate (%)",
      "Volunteer Hours",
      "Impact Numbers",
    ];
  }

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Get event data with date filtering
  const whereCondition = ngoId
    ? { ngo_id: ngoId, fund_raising_target: null }
    : { fund_raising_target: null };

  // Add date filtering for events (using event_date)
  if (startDate) {
    whereCondition.event_date = { [Op.gte]: new Date(startDate) };
  }
  if (endDate) {
    whereCondition.event_date = whereCondition.event_date
      ? {
          ...whereCondition.event_date,
          [Op.lte]: new Date(endDate + "T23:59:59.999Z"),
        }
      : { [Op.lte]: new Date(endDate + "T23:59:59.999Z") };
  }

  const events = await db.Campaign.findAll({
    where: whereCondition,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["name"],
      },
    ],
    order: [["event_date", "DESC"]],
  });

  // Add event data
  let currentRow = startRow + 1;
  for (const event of events) {
    // Get RSVP data for this event
    const rsvpData = await db.sequelize.query(
      `
      SELECT
        COUNT(CASE WHEN cr.rsvp_value = 'yes' THEN 1 END) as registrations,
        COUNT(CASE WHEN cr.type = 'Scanned' THEN 1 END) as actualAttendance,
        SUM(CASE WHEN cr.type = 'Scanned' AND cr.actualHours IS NOT NULL THEN cr.actualHours ELSE 0 END) as volunteerHours,
        SUM(CASE WHEN cr.type = 'Scanned' AND cr.impactHours IS NOT NULL THEN cr.impactHours ELSE 0 END) as impactHours
      FROM campaign_rsvps cr
      WHERE cr.campaign_id = :eventId
    `,
      {
        replacements: { eventId: event.id },
        type: QueryTypes.SELECT,
      }
    );

    const stats = rsvpData[0];
    const registrations = parseInt(stats.registrations || 0);
    const actualAttendance = parseInt(stats.actualAttendance || 0);
    const attendanceRate =
      registrations > 0
        ? ((actualAttendance / registrations) * 100).toFixed(2)
        : 0;

    if (!ngoId) {
      // Admin report with NGO column
      worksheet.getRow(currentRow).values = [
        event.ngoInfo?.name || "N/A",
        event.name,
        event.event_type || "N/A",
        event.event_date
          ? new Date(event.event_date).toLocaleDateString()
          : "N/A",
        registrations,
        `${attendanceRate}%`,
        parseFloat(stats.volunteerHours || 0),
        parseFloat(stats.impactHours || 0),
      ];
    } else {
      // NGO report without NGO column
      worksheet.getRow(currentRow).values = [
        event.name,
        event.event_type || "N/A",
        event.event_date
          ? new Date(event.event_date).toLocaleDateString()
          : "N/A",
        registrations,
        `${attendanceRate}%`,
        parseFloat(stats.volunteerHours || 0),
        parseFloat(stats.impactHours || 0),
      ];
    }
    currentRow++;
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 15;
  });
}

// Helper function to generate Platform Revenue Summary (Admin Only)
async function generatePlatformRevenueSummary(workbook) {
  const worksheet = workbook.addWorksheet("Platform Revenue Snapshot");

  // Set up headers and styling
  worksheet.mergeCells("A1:G1");
  worksheet.getCell("A1").value = "Platform Revenue Snapshot";
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Add generation timestamp
  worksheet.mergeCells("A2:G2");
  worksheet.getCell(
    "A2"
  ).value = `Generated on: ${new Date().toLocaleString()}`;
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  // Add headers
  const startRow = 4;
  worksheet.getRow(startRow).values = [
    "Date",
    "Gross Amount (₹)",
    "Platform Fees (₹)",
    "Gateway Fees (₹)",
    "Refunds (₹)",
    "Net to NGOs (₹)",
    "Transaction Count",
  ];

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Get daily revenue data for last 30 days
  const revenueData = await db.sequelize.query(
    `
    SELECT
      DATE(t.createdAt) as transactionDate,
      SUM(t.amount) as grossAmount,
      SUM(t.dr_tip_after_charges + t.user_convenience_after_charges + t.ngo_platform_convenience_fee) as platformFees,
      SUM(t.payment_gateway_charges) as gatewayFees,
      SUM(CASE WHEN t.status = 'refunded' THEN t.amount ELSE 0 END) as refunds,
      SUM(t.ngo_net_donation) as netToNgos,
      COUNT(t.id) as transactionCount
    FROM transactions t
    WHERE t.donation_type = 'campaign'
    GROUP BY DATE(t.createdAt)
    ORDER BY transactionDate DESC
  `,
    {
      type: QueryTypes.SELECT,
    }
  );

  // Add revenue data
  let currentRow = startRow + 1;
  for (const data of revenueData) {
    worksheet.getRow(currentRow).values = [
      new Date(data.transactionDate).toLocaleDateString(),
      parseFloat(data.grossAmount || 0),
      parseFloat(data.platformFees || 0),
      parseFloat(data.gatewayFees || 0),
      parseFloat(data.refunds || 0),
      parseFloat(data.netToNgos || 0),
      parseInt(data.transactionCount || 0),
    ];
    currentRow++;
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 15;
  });

  // Add summary row
  const summaryRow = currentRow + 1;
  worksheet.getCell(`A${summaryRow}`).value = "TOTAL";
  worksheet.getCell(`A${summaryRow}`).font = { bold: true };

  // Calculate totals
  const totals = await db.sequelize.query(
    `
    SELECT
      SUM(t.amount) as totalGross,
      SUM(t.dr_tip_after_charges + t.user_convenience_after_charges + t.ngo_platform_convenience_fee) as totalPlatformFees,
      SUM(t.payment_gateway_charges) as totalGatewayFees,
      SUM(CASE WHEN t.status = 'refunded' THEN t.amount ELSE 0 END) as totalRefunds,
      SUM(t.ngo_net_donation) as totalNetToNgos,
      COUNT(t.id) as totalTransactions
    FROM transactions t
    WHERE t.createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      AND t.donation_type = 'campaign'
  `,
    {
      type: QueryTypes.SELECT,
    }
  );

  const totalData = totals[0];
  worksheet.getRow(summaryRow).values = [
    "TOTAL",
    parseFloat(totalData.totalGross || 0),
    parseFloat(totalData.totalPlatformFees || 0),
    parseFloat(totalData.totalGatewayFees || 0),
    parseFloat(totalData.totalRefunds || 0),
    parseFloat(totalData.totalNetToNgos || 0),
    parseInt(totalData.totalTransactions || 0),
  ];

  // Style summary row
  const summaryRowObj = worksheet.getRow(summaryRow);
  summaryRowObj.font = { bold: true };
  summaryRowObj.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFCC00" },
  };
}

// Helper function to generate Transaction Log (NGO Only)
async function generateTransactionLogSheet(
  workbook,
  ngoId,
  startDate = null,
  endDate = null
) {
  const worksheet = workbook.addWorksheet("Donation Transaction Log");

  // Set up headers and styling
  worksheet.mergeCells("A1:L1");
  worksheet.getCell("A1").value = "Donation Transaction Log";
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Add generation timestamp
  worksheet.mergeCells("A2:L2");
  worksheet.getCell(
    "A2"
  ).value = `Generated on: ${new Date().toLocaleString()}`;
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  // Add headers
  const startRow = 4;
  worksheet.getRow(startRow).values = [
    "Transaction Date",
    "Campaign Name",
    "Donor Name",
    "Donor Email",
    "Gross Amount (₹)",
    "DR Tip (₹)",
    "User Convenience (₹)",
    "Gateway Charges (₹)",
    "Platform Fee (₹)",
    "Net Received (₹)",
    "Status",
    "Payment ID",
  ];

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Get transaction data for this NGO with date filtering
  let sqlQuery = `
    SELECT
      t.createdAt,
      c.name as campaignName,
      u.fullname as donorName,
      u.email as donorEmail,
      t.amount as grossAmount,
      t.dr_tip_after_charges as drTip,
      t.payment_gateway_charges as gatewayCharges,
      t.ngo_platform_convenience_fee as platformFee,
      t.ngo_net_donation as netReceived,
      t.status,
      t.razorpay_payment_id as paymentId
    FROM transactions t
    JOIN campaigns c ON t.campaign_id = c.id
    JOIN users u ON t.user_id = u.id
    WHERE t.ngo_id = :ngoId
      AND t.donation_type = 'campaign'
  `;

  const replacements = { ngoId };

  // Add date filtering
  if (startDate) {
    sqlQuery += ` AND DATE(t.createdAt) >= :startDate`;
    replacements.startDate = startDate;
  }
  if (endDate) {
    sqlQuery += ` AND DATE(t.createdAt) <= :endDate`;
    replacements.endDate = endDate;
  }

  sqlQuery += ` ORDER BY t.createdAt DESC`;

  const transactions = await db.sequelize.query(sqlQuery, {
    replacements,
    type: QueryTypes.SELECT,
  });

  // Add transaction data
  let currentRow = startRow + 1;
  for (const transaction of transactions) {
    worksheet.getRow(currentRow).values = [
      new Date(transaction.createdAt).toLocaleString(),
      transaction.campaignName,
      transaction.donorName || "Anonymous",
      transaction.donorEmail,
      parseFloat(transaction.grossAmount || 0),
      parseFloat(transaction.drTip || 0),
      parseFloat(transaction.gatewayCharges || 0),
      parseFloat(transaction.platformFee || 0),
      parseFloat(transaction.netReceived || 0),
      transaction.status.toUpperCase(),
      transaction.paymentId || "N/A",
    ];
    currentRow++;
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 15;
  });

  // Add summary row
  const summaryRow = currentRow + 1;
  worksheet.getCell(`A${summaryRow}`).value = "TOTAL";
  worksheet.getCell(`A${summaryRow}`).font = { bold: true };

  // Calculate totals
  const totals = await db.sequelize.query(
    `
    SELECT
      SUM(t.amount) as totalGross,
      SUM(t.dr_tip_after_charges) as totalDrTip,
      SUM(t.payment_gateway_charges) as totalGatewayCharges,
      SUM(t.ngo_platform_convenience_fee) as totalPlatformFee,
      SUM(t.ngo_net_donation) as totalNetReceived,
      COUNT(t.id) as totalTransactions
    FROM transactions t
    WHERE t.ngo_id = :ngoId
      AND t.donation_type = 'campaign'
      AND t.status = 'captured'
  `,
    {
      replacements: { ngoId },
      type: QueryTypes.SELECT,
    }
  );

  const totalData = totals[0];
  worksheet.getRow(summaryRow).values = [
    "TOTAL",
    "", // Campaign Name
    `${totalData.totalTransactions} Transactions`,
    "", // Donor Email
    parseFloat(totalData.totalGross || 0),
    parseFloat(totalData.totalDrTip || 0),
    parseFloat(totalData.totalGatewayCharges || 0),
    parseFloat(totalData.totalPlatformFee || 0),
    parseFloat(totalData.totalNetReceived || 0),
    "CAPTURED",
    "",
  ];

  // Style summary row
  const summaryRowObj = worksheet.getRow(summaryRow);
  summaryRowObj.font = { bold: true };
  summaryRowObj.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFCC00" },
  };
}

// Generate Audit Logging Report
async function generateAuditLoggingReport(
  donationType = "all",
  status = "all",
  ngoId = null,
  isDonorWithinMaharashtra = null,
  isNgoWithinMaharashtra = null,
  startDate = null,
  endDate = null
) {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");

    let fileName, reportTitle;
    if (ngoId) {
      fileName = `NGO_Audit_Log_${ngoId}_${donationType}_${status}_${timestamp}.xlsx`;
      reportTitle = "NGO Donation Audit Log";
    } else {
      fileName = `Admin_Audit_Log_${donationType}_${status}_${timestamp}.xlsx`;
      reportTitle = "Platform Donation Audit Log";
    }

    await generateAuditLogSheet(
      workbook,
      donationType,
      status,
      ngoId,
      isDonorWithinMaharashtra,
      isNgoWithinMaharashtra,
      reportTitle,
      startDate,
      endDate
    );

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
    };
  } catch (error) {
    console.error("Error generating audit logging report:", error);
    throw error;
  }
}

// Helper function to generate Audit Log Sheet
async function generateAuditLogSheet(
  workbook,
  donationType,
  status,
  ngoId,
  isDonorWithinMaharashtra,
  isNgoWithinMaharashtra,
  reportTitle,
  startDate = null,
  endDate = null
) {
  const worksheet = workbook.addWorksheet("Donation Audit Log");

  // Set up headers and styling
  worksheet.mergeCells("A1:T1");
  worksheet.getCell("A1").value = reportTitle;
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Add generation timestamp and filters
  worksheet.mergeCells("A2:T2");
  worksheet.getCell(
    "A2"
  ).value = `Generated on: ${new Date().toLocaleString()}`;
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  worksheet.mergeCells("A3:T3");
  const filterText = `Filters: Type=${donationType}, Status=${status}${
    ngoId ? `, NGO ID=${ngoId}` : ""
  }${
    isDonorWithinMaharashtra !== null
      ? `, Donor in MH=${isDonorWithinMaharashtra}`
      : ""
  }${
    isNgoWithinMaharashtra !== null
      ? `, NGO in MH=${isNgoWithinMaharashtra}`
      : ""
  }`;
  worksheet.getCell("A3").value = filterText;
  worksheet.getCell("A3").alignment = { horizontal: "center" };

  // Where conditions are built directly in the SQL query below

  // Add headers based on admin vs NGO view
  const startRow = 5;
  if (!ngoId) {
    // Admin report with all columns including NGO info
    worksheet.getRow(startRow).values = [
      "Transaction Date",
      "Donation Type",
      "Status",
      "Donor Name",
      "Donor Email",
      "NGO/Campaign/Bucket Info",
      "Gross Amount (₹)",
      "Base Amount (₹)",
      "DR Tip (₹)",
      "DR Tip GST (₹)",
      "User Convenience (₹)",
      "User Conv GST (₹)",
      "Gateway Charges (₹)",
      "Platform Fee (₹)",
      "Net to NGO (₹)",
      "Donor in MH",
      "NGO in MH",
      "GST Type",
      "Payment ID",
      "Impact Created",
    ];
  } else {
    // NGO report without NGO info column
    worksheet.getRow(startRow).values = [
      "Transaction Date",
      "Donation Type",
      "Status",
      "Donor Name",
      "Donor Email",
      "Campaign/Bucket Info",
      "Gross Amount (₹)",
      "Base Amount (₹)",
      "DR Tip (₹)",
      "DR Tip GST (₹)",
      "User Convenience (₹)",
      "User Conv GST (₹)",
      "Gateway Charges (₹)",
      "Platform Fee (₹)",
      "Net Received (₹)",
      "Donor in MH",
      "NGO in MH",
      "GST Type",
      "Payment ID",
      "Impact Created",
    ];
  }

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Build SQL query with proper joins to avoid association conflicts
  let sqlQuery = `
    SELECT
      t.*,
      u.fullname as donor_name,
      u.email as donor_email,
      n.name as ngo_name,
      c.name as campaign_name,
      b.name as bucket_name,
      b.description as bucket_description
    FROM transactions t
    LEFT JOIN users u ON t.user_id = u.id
    LEFT JOIN ngos n ON t.ngo_id = n.id
    LEFT JOIN campaigns c ON t.campaign_id = c.id
    LEFT JOIN buckets b ON t.bucket_id = b.id
    WHERE 1=1
  `;

  const replacements = {};

  // Add where conditions to SQL
  if (donationType !== "all") {
    sqlQuery += ` AND t.donation_type = :donationType`;
    replacements.donationType = donationType;
  } else {
    sqlQuery += ` AND t.donation_type IN ('ngo', 'campaign', 'genericViaMoney')`;
  }

  if (status === "captured") {
    sqlQuery += ` AND t.status = 'captured'`;
  } else if (status === "failed") {
    sqlQuery += ` AND t.status != 'captured'`;
  }

  if (ngoId) {
    sqlQuery += ` AND t.ngo_id = :ngoId`;
    replacements.ngoId = ngoId;
  }

  if (isDonorWithinMaharashtra !== null) {
    sqlQuery += ` AND t.is_donor_within_maharashtra = :isDonorWithinMaharashtra`;
    replacements.isDonorWithinMaharashtra = isDonorWithinMaharashtra;
  }

  if (isNgoWithinMaharashtra !== null) {
    sqlQuery += ` AND t.is_ngo_within_maharashtra = :isNgoWithinMaharashtra`;
    replacements.isNgoWithinMaharashtra = isNgoWithinMaharashtra;
  }

  // Add date filtering
  if (startDate) {
    sqlQuery += ` AND DATE(t.createdAt) >= :startDate`;
    replacements.startDate = startDate;
  }
  if (endDate) {
    sqlQuery += ` AND DATE(t.createdAt) <= :endDate`;
    replacements.endDate = endDate;
  }

  sqlQuery += ` ORDER BY t.createdAt DESC`;

  const transactions = await db.sequelize.query(sqlQuery, {
    replacements,
    type: QueryTypes.SELECT,
  });

  // Add transaction data
  let currentRow = startRow + 1;
  for (const transaction of transactions) {
    // Calculate base amount (amount - DR tip)
    const drTipTotal =
      (transaction.dr_tip_base_amount || 0) +
      (transaction.dr_tip_total_gst || 0);
    const baseAmount = transaction.amount - drTipTotal;

    // Get donation type specific info
    let donationInfo = "";
    if (transaction.donation_type === "ngo") {
      donationInfo = transaction.ngo_name || "N/A";
    } else if (transaction.donation_type === "campaign") {
      donationInfo = transaction.campaign_name || "N/A";
    } else if (transaction.donation_type === "genericViaMoney") {
      donationInfo = transaction.bucket_name || "N/A";
    }

    const rowData = [
      new Date(transaction.createdAt).toLocaleString(),
      transaction.donation_type.toUpperCase(),
      transaction.status.toUpperCase(),
      transaction.donor_name || "Anonymous",
      transaction.donor_email || "N/A",
      donationInfo,
      parseFloat(transaction.amount || 0),
      parseFloat(baseAmount || 0),
      parseFloat(
        (transaction.dr_tip_base_amount || 0) +
          (transaction.dr_tip_total_gst || 0)
      ),
      parseFloat(transaction.dr_tip_total_gst || 0),
      parseFloat(
        (transaction.user_convenience_base_amount || 0) +
          (transaction.user_convenience_total_gst || 0)
      ),
      parseFloat(transaction.user_convenience_total_gst || 0),
      parseFloat(transaction.payment_gateway_charges || 0),
      parseFloat(transaction.ngo_platform_convenience_fee || 0),
      parseFloat(transaction.ngo_net_donation || 0),
      transaction.is_donor_within_maharashtra ? "Yes" : "No",
      transaction.is_ngo_within_maharashtra ? "Yes" : "No",
      transaction.dr_tip_gst_type ||
        transaction.user_convenience_gst_type ||
        "N/A",
      transaction.razorpay_payment_id || "N/A",
      parseInt(transaction.impact_created || 0),
    ];

    worksheet.getRow(currentRow).values = rowData;
    currentRow++;
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 12;
  });

  // Add summary row
  const summaryRow = currentRow + 1;
  worksheet.getCell(`A${summaryRow}`).value = "SUMMARY";
  worksheet.getCell(`A${summaryRow}`).font = { bold: true };

  // Calculate totals
  const totals = transactions.reduce(
    (acc, transaction) => {
      if (transaction.status === "captured") {
        acc.totalGross += parseFloat(transaction.amount || 0);
        acc.totalDrTip += parseFloat(
          (transaction.dr_tip_base_amount || 0) +
            (transaction.dr_tip_total_gst || 0)
        );

        acc.totalGatewayCharges += parseFloat(
          transaction.payment_gateway_charges || 0
        );
        acc.totalPlatformFee += parseFloat(
          transaction.ngo_platform_convenience_fee || 0
        );
        acc.totalNetToNgo += parseFloat(transaction.ngo_net_donation || 0);
        acc.totalImpact += parseInt(transaction.impact_created || 0);
        acc.capturedCount++;
      } else {
        acc.failedCount++;
      }
      acc.totalTransactions++;
      return acc;
    },
    {
      totalGross: 0,
      totalDrTip: 0,
      totalGatewayCharges: 0,
      totalPlatformFee: 0,
      totalNetToNgo: 0,
      totalImpact: 0,
      capturedCount: 0,
      failedCount: 0,
      totalTransactions: 0,
    }
  );

  worksheet.getRow(summaryRow).values = [
    "SUMMARY",
    `${totals.totalTransactions} Total`,
    `${totals.capturedCount} Captured, ${totals.failedCount} Failed`,
    "",
    "",
    "", // Empty cells for donor info
    totals.totalGross,
    "", // Base amount calculation would be complex for summary
    totals.totalDrTip,
    "", // DR Tip GST
    "", // User Conv GST
    totals.totalGatewayCharges,
    totals.totalPlatformFee,
    totals.totalNetToNgo,
    "",
    "",
    "",
    "", // Location and other info
    totals.totalImpact,
  ];

  // Style summary row
  const summaryRowObj = worksheet.getRow(summaryRow);
  summaryRowObj.font = { bold: true };
  summaryRowObj.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFFFCC00" },
  };
}

// Generate Campaign Donor Report (Individual Campaign)
async function generateCampaignDonorReport(
  campaignId,
  startDate = null,
  endDate = null
) {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");

    // Get campaign details first
    const campaign = await db.Campaign.findByPk(campaignId, {
      include: [
        {
          model: db.Ngo,
          as: "ngoInfo",
          attributes: ["name", "email"],
        },
      ],
    });

    if (!campaign) {
      throw new Error(`Campaign with ID ${campaignId} not found`);
    }

    const fileName = `Campaign_Donor_Report_${campaignId}_${timestamp}.xlsx`;
    const reportTitle = `Donor Report - ${campaign.name}`;

    await generateCampaignDonorSheet(
      workbook,
      campaignId,
      campaign,
      reportTitle,
      startDate,
      endDate
    );

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
      campaignInfo: {
        id: campaign.id,
        name: campaign.name,
        ngoName: campaign.ngoInfo?.name || "N/A",
      },
      filters: {
        campaignId,
        startDate,
        endDate,
      },
    };
  } catch (error) {
    console.error("Error generating campaign donor report:", error);
    throw error;
  }
}

// Generate Event Volunteer Report (Individual Event)
async function generateEventVolunteerReport(
  eventId,
  startDate = null,
  endDate = null
) {
  try {
    const workbook = new ExcelJS.Workbook();
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");

    // Get event details first
    const event = await db.Campaign.findByPk(eventId, {
      include: [
        {
          model: db.Ngo,
          as: "ngoInfo",
          attributes: ["name", "email"],
        },
      ],
      where: {
        fund_raising_target: null, // Events have null fund_raising_target
      },
    });

    if (!event) {
      throw new Error(`Event with ID ${eventId} not found`);
    }

    const fileName = `Event_Volunteer_Report_${eventId}_${timestamp}.xlsx`;
    const reportTitle = `Volunteer Report - ${event.name}`;

    await generateEventVolunteerSheet(
      workbook,
      eventId,
      event,
      reportTitle,
      startDate,
      endDate
    );

    const filePath = path.join(__dirname, "../../uploads/reports", fileName);

    // Ensure reports directory exists
    const reportsDir = path.dirname(filePath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    await workbook.xlsx.writeFile(filePath);

    return {
      success: true,
      fileName: fileName,
      downloadUrl: `/uploads/reports/${fileName}`,
      filePath: filePath,
      eventInfo: {
        id: event.id,
        name: event.name,
        ngoName: event.ngoInfo?.name || "N/A",
        eventDate: event.event_date,
      },
      filters: {
        eventId,
        startDate,
        endDate,
      },
    };
  } catch (error) {
    console.error("Error generating event volunteer report:", error);
    throw error;
  }
}

// Helper function to generate Campaign Donor Sheet
async function generateCampaignDonorSheet(
  workbook,
  campaignId,
  campaign,
  reportTitle,
  startDate = null,
  endDate = null
) {
  const worksheet = workbook.addWorksheet("Campaign Donor Report");

  // Set up headers and styling
  worksheet.mergeCells("A1:J1");
  worksheet.getCell("A1").value = reportTitle;
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Campaign info
  worksheet.mergeCells("A2:J2");
  worksheet.getCell("A2").value = `Campaign: ${campaign.name} | NGO: ${
    campaign.ngoInfo?.name || "N/A"
  }`;
  worksheet.getCell("A2").font = { size: 12, bold: true };
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  // Date range info if provided
  if (startDate || endDate) {
    worksheet.mergeCells("A3:J3");
    let dateInfo = "Date Range: ";
    if (startDate && endDate) {
      dateInfo += `${startDate} to ${endDate}`;
    } else if (startDate) {
      dateInfo += `From ${startDate}`;
    } else if (endDate) {
      dateInfo += `Up to ${endDate}`;
    }
    worksheet.getCell("A3").value = dateInfo;
    worksheet.getCell("A3").font = { size: 10, italic: true };
    worksheet.getCell("A3").alignment = { horizontal: "center" };
  }

  const startRow = startDate || endDate ? 5 : 4;

  // Headers
  worksheet.getRow(startRow).values = [
    "Donor Name",
    "Email",
    "Mobile",
    "Donation Date",
    "Gross Amount (₹)",
    "DR Tip (₹)",
    "User Convenience (₹)",
    "Gateway Charges (₹)",
    "Platform Fee (₹)",
    "Net to NGO (₹)",
    "Status",
    "Payment ID",
  ];

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Get donor data with date filtering
  let sqlQuery = `
    SELECT
      u.fullname as donor_name,
      u.email as donor_email,
      u.mobile_number as donor_mobile,
      t.createdAt as donation_date,
      t.amount as gross_amount,
      t.dr_tip_after_charges as dr_tip,
      t.payment_gateway_charges as gateway_charges,
      t.ngo_platform_convenience_fee as platform_fee,
      t.ngo_net_donation as net_to_ngo,
      t.status,
      t.razorpay_payment_id as payment_id
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    WHERE t.campaign_id = :campaignId
      AND t.donation_type = 'campaign'
      AND t.status = 'captured'
  `;

  const replacements = { campaignId };

  // Add date filtering
  if (startDate) {
    sqlQuery += ` AND DATE(t.createdAt) >= :startDate`;
    replacements.startDate = startDate;
  }
  if (endDate) {
    sqlQuery += ` AND DATE(t.createdAt) <= :endDate`;
    replacements.endDate = endDate;
  }

  // Sort by highest donation first
  sqlQuery += ` ORDER BY t.amount DESC`;

  const donors = await db.sequelize.query(sqlQuery, {
    replacements,
    type: QueryTypes.SELECT,
  });

  // Add donor data
  let currentRow = startRow + 1;
  let totalGross = 0;
  let totalDrTip = 0;
  let totalGatewayCharges = 0;
  let totalPlatformFee = 0;
  let totalNetToNgo = 0;

  for (const donor of donors) {
    worksheet.getRow(currentRow).values = [
      donor.donor_name || "Anonymous",
      donor.donor_email || "N/A",
      donor.donor_mobile || "N/A",
      new Date(donor.donation_date).toLocaleDateString(),
      parseFloat(donor.gross_amount || 0),
      parseFloat(donor.dr_tip || 0),
      parseFloat(donor.gateway_charges || 0),
      parseFloat(donor.platform_fee || 0),
      parseFloat(donor.net_to_ngo || 0),
      donor.status,
      donor.payment_id || "N/A",
    ];

    // Add to totals
    totalGross += parseFloat(donor.gross_amount || 0);
    totalDrTip += parseFloat(donor.dr_tip || 0);
    totalGatewayCharges += parseFloat(donor.gateway_charges || 0);
    totalPlatformFee += parseFloat(donor.platform_fee || 0);
    totalNetToNgo += parseFloat(donor.net_to_ngo || 0);

    currentRow++;
  }

  // Add summary row
  if (donors.length > 0) {
    currentRow++; // Empty row
    worksheet.getRow(currentRow).values = [
      "TOTAL",
      `${donors.length} Donors`,
      "",
      "",
      totalGross,
      totalDrTip,
      totalGatewayCharges,
      totalPlatformFee,
      totalNetToNgo,
      "",
      "",
    ];

    // Style summary row
    const summaryRow = worksheet.getRow(currentRow);
    summaryRow.font = { bold: true };
    summaryRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFFFCC00" },
    };
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 15;
  });

  // Add borders to all cells with data
  for (let row = startRow; row <= currentRow; row++) {
    for (let col = 1; col <= 12; col++) {
      worksheet.getCell(row, col).border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    }
  }
}

// Helper function to generate Event Volunteer Sheet
async function generateEventVolunteerSheet(
  workbook,
  eventId,
  event,
  reportTitle,
  startDate = null,
  endDate = null
) {
  const worksheet = workbook.addWorksheet("Event Volunteer Report");

  // Set up headers and styling
  worksheet.mergeCells("A1:K1");
  worksheet.getCell("A1").value = reportTitle;
  worksheet.getCell("A1").font = { size: 16, bold: true };
  worksheet.getCell("A1").alignment = { horizontal: "center" };

  // Event info
  worksheet.mergeCells("A2:K2");
  worksheet.getCell("A2").value = `Event: ${event.name} | NGO: ${
    event.ngoInfo?.name || "N/A"
  } | Date: ${
    event.event_date ? new Date(event.event_date).toLocaleDateString() : "N/A"
  }`;
  worksheet.getCell("A2").font = { size: 12, bold: true };
  worksheet.getCell("A2").alignment = { horizontal: "center" };

  // Date range info if provided
  if (startDate || endDate) {
    worksheet.mergeCells("A3:K3");
    let dateInfo = "RSVP Date Range: ";
    if (startDate && endDate) {
      dateInfo += `${startDate} to ${endDate}`;
    } else if (startDate) {
      dateInfo += `From ${startDate}`;
    } else if (endDate) {
      dateInfo += `Up to ${endDate}`;
    }
    worksheet.getCell("A3").value = dateInfo;
    worksheet.getCell("A3").font = { size: 10, italic: true };
    worksheet.getCell("A3").alignment = { horizontal: "center" };
  }

  const startRow = startDate || endDate ? 5 : 4;

  // Headers
  worksheet.getRow(startRow).values = [
    "Volunteer Name",
    "Email",
    "Mobile",
    "Gender",
    "Age",
    "RSVP Date",
    "RSVP Status",
    "Type",
    "Impact Hours",
    "Actual Hours",
    "Location",
  ];

  // Style headers
  const headerRow = worksheet.getRow(startRow);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Get volunteer data with date filtering
  let sqlQuery = `
    SELECT
      u.fullname as volunteer_name,
      u.email as volunteer_email,
      u.mobile_number as volunteer_mobile,
      u.gender,
      u.age,
      u.place_name as location,
      cr.createdAt as rsvp_date,
      cr.rsvp_value,
      cr.type,
      cr.impactHours,
      cr.actualHours
    FROM campaign_rsvps cr
    JOIN users u ON cr.user_id = u.id
    WHERE cr.campaign_id = :eventId
  `;

  const replacements = { eventId };

  // Add date filtering for RSVP date
  if (startDate) {
    sqlQuery += ` AND DATE(cr.createdAt) >= :startDate`;
    replacements.startDate = startDate;
  }
  if (endDate) {
    sqlQuery += ` AND DATE(cr.createdAt) <= :endDate`;
    replacements.endDate = endDate;
  }

  // Sort by highest impact hours first, then by actual hours
  sqlQuery += ` ORDER BY
    CASE WHEN cr.impactHours IS NOT NULL THEN cr.impactHours ELSE 0 END DESC,
    CASE WHEN cr.actualHours IS NOT NULL THEN cr.actualHours ELSE 0 END DESC,
    cr.createdAt DESC`;

  const volunteers = await db.sequelize.query(sqlQuery, {
    replacements,
    type: QueryTypes.SELECT,
  });

  // Add volunteer data
  let currentRow = startRow + 1;
  let totalImpactHours = 0;
  let totalActualHours = 0;
  let rsvpYesCount = 0;
  let scannedCount = 0;

  for (const volunteer of volunteers) {
    worksheet.getRow(currentRow).values = [
      volunteer.volunteer_name || "Anonymous",
      volunteer.volunteer_email || "N/A",
      volunteer.volunteer_mobile || "N/A",
      volunteer.gender || "N/A",
      volunteer.age || "N/A",
      new Date(volunteer.rsvp_date).toLocaleDateString(),
      volunteer.rsvp_value || "N/A",
      volunteer.type || "N/A",
      parseInt(volunteer.impactHours || 0),
      parseInt(volunteer.actualHours || 0),
      volunteer.location || "N/A",
    ];

    // Add to totals
    totalImpactHours += parseInt(volunteer.impactHours || 0);
    totalActualHours += parseInt(volunteer.actualHours || 0);

    if (volunteer.rsvp_value === "yes") {
      rsvpYesCount++;
    }
    if (volunteer.type === "Scanned") {
      scannedCount++;
    }

    currentRow++;
  }

  // Add summary rows
  if (volunteers.length > 0) {
    currentRow++; // Empty row

    // Summary statistics
    worksheet.getRow(currentRow).values = [
      "SUMMARY",
      `Total: ${volunteers.length}`,
      `RSVP Yes: ${rsvpYesCount}`,
      `Attended: ${scannedCount}`,
      `Conversion: ${
        rsvpYesCount > 0
          ? ((scannedCount / rsvpYesCount) * 100).toFixed(1) + "%"
          : "0%"
      }`,
      "",
      "",
      "",
      totalImpactHours,
      totalActualHours,
      "",
    ];

    // Style summary row
    const summaryRow = worksheet.getRow(currentRow);
    summaryRow.font = { bold: true };
    summaryRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFFFCC00" },
    };
  }

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    column.width = 15;
  });

  // Add borders to all cells with data
  for (let row = startRow; row <= currentRow; row++) {
    for (let col = 1; col <= 11; col++) {
      worksheet.getCell(row, col).border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    }
  }
}
