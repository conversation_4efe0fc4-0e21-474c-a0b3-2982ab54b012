﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const config = require("../../config.json");
const axios = require("axios")

const KEY_ID = config.RAZORPAY_KEY_ID;
const KEY_SECRET = config.RAZORPAY_KEY_SECRET;
const auth = Buffer.from(`${KEY_ID}:${KEY_SECRET}`).toString("base64");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getRazorpayAccountStatusByNgoId,
};

db.BankDetail.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});

async function getAll(params, isadmin) {
  const where = {};
  const { ngoId } = params;
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  return await db.BankDetail.findAll({
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name", "ngo_status"],
      },
    ],
    where: where,
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate

  const record = await db.BankDetail.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.BankDetail.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}

async function getRazorpayAccountStatusByNgoId(params) {
  const { ngo_id } = params;
  if (!ngo_id) throw new Error("ngo_id is required");

  // Step 1: Fetch bank details for ngo_id
  const bankDetail = await db.BankDetail.findOne({
    where: { ngo_id },
  });

  if (!bankDetail) throw new Error("Bank details not found for the NGO");

  const { accountId, productId } = bankDetail;

  if (!accountId || !productId) {
    throw new Error("Razorpay account or product ID is missing in bank detail");
  }

  const url = `https://api.razorpay.com/v2/accounts/${accountId}/products/${productId}`;

  const response = await axios.get(url, {
    headers: {
      Authorization: `Basic ${auth}`,
    },
  });

  const activationStatus = response.data.activation_status;
  const isActivated = activationStatus === "activated";

  return {
    activation_status: activationStatus,
    isActivated,
  };
}
