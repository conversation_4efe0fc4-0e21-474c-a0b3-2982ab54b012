const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const paymentTransferService = require("./payment-transfer.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

// Create transfer record
function create(req, res, next) {
  paymentTransferService
    .create(req.body)
    .then(() => {
      logAction(5, `Added a payment transfer record`);
      return res.json({
        status: true,
        message: "Transfer record created successfully",
      });
    })
    .catch(next);
}

// Get all transfers with optional filters (ngoId, paymentId, transferId)
function getAll(req, res, next) {
  paymentTransferService
    .getAll(req.query)
    .then((records) => {
      logAction(5, `Fetched all payment transfers`, req.query.pageName);
      return res.json(records);
    })
    .catch(next);
}

// Get by ID
function getById(req, res, next) {
  paymentTransferService
    .getById(req.params.id)
    .then((record) => res.json(record))
    .catch(next);
}

// Schema for create
function updateSchema(req, res, next) {
  const schema = Joi.object({
    payment_id: Joi.string().optional(),
    order_id: Joi.number().allow(null), // INTEGER in model
    transaction_id: Joi.number().allow(null), // INTEGER in model
    ngo_account_id: Joi.string().optional(),
    ngo_id: Joi.number().optional(), // INTEGER in model
    amount: Joi.number().optional(), // DECIMAL -> validate as number
    currency: Joi.string().max(5).default("INR"),
    transfer_id: Joi.string().allow(null, ""),
    status: Joi.string().valid("INITIATED", "SUCCESS", "FAILED"),
    notes: Joi.string().allow(null, ""), // STRING in model
    response_data: Joi.string().allow(null, ""), // STRING in model
  });

  validateRequest(req, next, schema);
}

// Schema for update
function updateSchema(req, res, next) {
  const schema = Joi.object({
    payment_id: Joi.string().optional(),
    order_id: Joi.number().allow(null), // INTEGER in model
    transaction_id: Joi.number().allow(null), // INTEGER in model
    ngo_account_id: Joi.string().optional(),
    ngo_id: Joi.number().optional(), // INTEGER in model
    amount: Joi.number().optional(), // DECIMAL -> validate as number
    currency: Joi.string().max(5).default("INR"),
    transfer_id: Joi.string().allow(null, ""),
    status: Joi.string().valid("INITIATED", "SUCCESS", "FAILED"),
    notes: Joi.string().allow(null, ""), // STRING in model
    response_data: Joi.string().allow(null, ""), // STRING in model
  });

  validateRequest(req, next, schema);
}

// Update
function update(req, res, next) {
  paymentTransferService
    .update(req.params.id, req.body)
    .then((record) => res.json(record))
    .catch(next);
}

// Delete
function _delete(req, res, next) {
  paymentTransferService
    .delete(req.params.id)
    .then(() =>
      res.json({
        status: true,
        message: "Transfer record deleted successfully",
      })
    )
    .catch(next);
}
