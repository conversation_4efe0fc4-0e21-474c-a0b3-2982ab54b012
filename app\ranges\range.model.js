const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    user_id: { type: DataTypes.INTEGER, allowNull: false },
    moneyStartRange: { type: DataTypes.INTEGER, allowNull: false },
    moneyEndRange: { type: DataTypes.INTEGER, allowNull: false },
    timeStart: { type: DataTypes.STRING, allowNull: false },
    timeEnd: { type: DataTypes.STRING, allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("ranges", attributes, options);
} 