// googleAuth.js
const { google } = require("googleapis");
const fs = require("fs");
const path = require("path");
const config = require("../../config.json");
const readline = require("readline");

const TOKEN_PATH = path.join(__dirname, "token.json"); // Save tokens here

const oauth2Client = new google.auth.OAuth2(
  config.GOOGLE_API_CLIENT_ID,
  config.GOOGLE_API_CLIENT_SECRET,
  config.GOOGLE_API_REDIRECT_URI
);

// This method loads the client and handles token persistence
function getAuthorizedClient() {
  if (!fs.existsSync(TOKEN_PATH)) {
    throw new Error("Token file not found. Please authorize the application.");
  }

  const token = JSON.parse(fs.readFileSync(TOKEN_PATH, "utf8"));
  oauth2Client.setCredentials(token);

  // Automatically save updated tokens when refreshed
  oauth2Client.on("tokens", (newTokens) => {
    if (newTokens.refresh_token) {
      token.refresh_token = newTokens.refresh_token;
    }
    if (newTokens.access_token) {
      token.access_token = newTokens.access_token;
    }
    fs.writeFileSync(TOKEN_PATH, JSON.stringify(token, null, 2));
  });

  return oauth2Client;
}

// This method safely retrieves an access token, handling all error scenarios
async function getAccessToken() {
  try {
    const client = getAuthorizedClient();

    // This will refresh the token if needed
    const { token } = await client.getAccessToken();

    if (!token) {
      throw new Error("Failed to retrieve access token");
    }

    return token;
  } catch (error) {
    const isInvalidGrant =
      error?.response?.data?.error === "invalid_grant" ||
      error?.message?.includes("invalid_grant");

    console.log("getAccessToken error:", error.response?.data || error.message);

    if (isInvalidGrant) {
      console.error(
        "❌ Refresh token is expired or revoked. Re-authentication is required."
      );

      // ✅ Attempt to reset token by re-authentication
      try {
        const newTokens = await resetToken(); // ← call re-auth flow
        return newTokens.access_token;
      } catch (authError) {
        console.error("❌ Re-authentication failed:", authError.message);
        throw authError;
      }
    } else {
      console.error("❌ Error while getting access token:", error.message);
      throw error;
    }
  }
}

async function resetToken() {
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: "offline",
    scope: [
      "https://www.googleapis.com/auth/photoslibrary.appendonly",
      "https://www.googleapis.com/auth/photoslibrary",
      "https://www.googleapis.com/auth/photoslibrary.readonly",
    ],
    prompt: "consent",
  });

  console.log("🔐 Authorize this app by visiting this URL:\n", authUrl);

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve, reject) => {
    rl.question("\nPaste the authorization code here: ", async (code) => {
      rl.close();
      try {
        const { tokens } = await oauth2Client.getToken(code);
        oauth2Client.setCredentials(tokens);

        fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens, null, 2));
        console.log("✅ New token saved to", TOKEN_PATH);

        resolve(tokens);
      } catch (err) {
        console.error("❌ Failed to retrieve new tokens:", err.message);
        reject(err);
      }
    });
  });
}

module.exports = {
  getAccessToken,
};
