const reportsService = require("./reports.service");
const express = require("express");
const router = express.Router();

// Campaign Reports
router.get("/campaigns", generateCampaignReport);

// Event Reports
router.get("/events", generateEventReport);

// Platform Revenue Report (Admin Only)
router.get("/platform-revenue", generatePlatformRevenueReport);

// Donation Transaction Log (NGO Only)
router.get("/transaction-log", generateDonationTransactionLog);

// Audit Logging Report (Admin and NGO)
router.get("/audit-log", generateAuditLoggingReport);

// Individual Campaign Donor Report
router.get("/campaign-donors/:campaignId", generateCampaignDonorReport);

// Individual Event Volunteer Report
router.get("/event-volunteers/:eventId", generateEventVolunteerReport);

module.exports = router;

// Generate Campaign Reports
function generateCampaignReport(req, res, next) {
  const { reportType, ngoId, startDate, endDate } = req.query;

  // Validate report type
  const validCampaignReports = ["campaign_financial_summary"];
  if (!reportType || !validCampaignReports.includes(reportType)) {
    return res.status(400).json({
      success: false,
      message:
        "Invalid or missing report type. Valid types: campaign_financial_summary",
    });
  }

  // Validate date format if provided
  if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid startDate format. Use YYYY-MM-DD format.",
    });
  }

  if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid endDate format. Use YYYY-MM-DD format.",
    });
  }

  // For admin reports, ngoId is optional
  // For NGO reports, ngoId should be provided (but we'll handle both cases)

  reportsService
    .generateCampaignReport(reportType, ngoId, startDate, endDate)
    .then((result) => {
      const logMessage = ngoId
        ? `Generated ${reportType} report for NGO: ${ngoId}`
        : `Generated ${reportType} report for all campaigns (Admin)`;

      res.json({
        success: true,
        message: "Campaign report generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
        },
      });
    })
    .catch(next);
}

// Generate Event Reports
function generateEventReport(req, res, next) {
  const { reportType, ngoId, startDate, endDate } = req.query;

  // Validate report type
  const validEventReports = ["event_attendance_summary"];
  if (!reportType || !validEventReports.includes(reportType)) {
    return res.status(400).json({
      success: false,
      message:
        "Invalid or missing report type. Valid types: event_attendance_summary",
    });
  }

  // Validate date format if provided
  if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid startDate format. Use YYYY-MM-DD format.",
    });
  }

  if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid endDate format. Use YYYY-MM-DD format.",
    });
  }

  reportsService
    .generateEventReport(reportType, ngoId, startDate, endDate)
    .then((result) => {
      const logMessage = ngoId
        ? `Generated ${reportType} report for NGO: ${ngoId}`
        : `Generated ${reportType} report for all events (Admin)`;

      res.json({
        success: true,
        message: "Event report generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
        },
      });
    })
    .catch(next);
}

// Generate Platform Revenue Report (Admin Only)
function generatePlatformRevenueReport(req, res, next) {
  // This is admin-only report, no ngoId needed

  reportsService
    .generatePlatformRevenueReport()
    .then((result) => {
      res.json({
        success: true,
        message: "Platform revenue report generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
        },
      });
    })
    .catch(next);
}

// Generate Donation Transaction Log (NGO Only)
function generateDonationTransactionLog(req, res, next) {
  const { ngoId, startDate, endDate } = req.query;

  if (!ngoId) {
    return res.status(400).json({
      success: false,
      message: "NGO ID is required for transaction log report",
    });
  }

  // Validate date format if provided
  if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid startDate format. Use YYYY-MM-DD format.",
    });
  }

  if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid endDate format. Use YYYY-MM-DD format.",
    });
  }

  reportsService
    .generateDonationTransactionLog(ngoId, startDate, endDate)
    .then((result) => {
      res.json({
        success: true,
        message: "Donation transaction log generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
        },
      });
    })
    .catch(next);
}

// Generate Audit Logging Report
function generateAuditLoggingReport(req, res, next) {
  const {
    donationType = "all",
    status = "all",
    ngoId,
    isDonorWithinMaharashtra,
    isNgoWithinMaharashtra,
    startDate,
    endDate,
  } = req.query;

  // Validate donation type
  const validDonationTypes = ["all", "ngo", "campaign", "genericViaMoney"];
  if (!validDonationTypes.includes(donationType)) {
    return res.status(400).json({
      success: false,
      message:
        "Invalid donation type. Valid types: all, ngo, campaign, genericViaMoney",
    });
  }

  // Validate status
  const validStatuses = ["all", "captured", "failed"];
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      success: false,
      message: "Invalid status. Valid statuses: all, captured, failed",
    });
  }

  // Validate date format if provided
  if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid startDate format. Use YYYY-MM-DD format.",
    });
  }

  if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid endDate format. Use YYYY-MM-DD format.",
    });
  }

  // Convert string boolean parameters to actual booleans
  const donorInMH =
    isDonorWithinMaharashtra === "true"
      ? true
      : isDonorWithinMaharashtra === "false"
      ? false
      : null;
  const ngoInMH =
    isNgoWithinMaharashtra === "true"
      ? true
      : isNgoWithinMaharashtra === "false"
      ? false
      : null;

  reportsService
    .generateAuditLoggingReport(
      donationType,
      status,
      ngoId,
      donorInMH,
      ngoInMH,
      startDate,
      endDate
    )
    .then((result) => {
      const logMessage = ngoId
        ? `Generated audit log report for NGO: ${ngoId}, Type: ${donationType}, Status: ${status}`
        : `Generated audit log report for all donations (Admin), Type: ${donationType}, Status: ${status}`;

      res.json({
        success: true,
        message: "Audit logging report generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
          filters: {
            donationType,
            status,
            ngoId: ngoId || "all",
            isDonorWithinMaharashtra: donorInMH,
            isNgoWithinMaharashtra: ngoInMH,
          },
        },
      });
    })
    .catch(next);
}

// Generate Campaign Donor Report (Individual Campaign)
function generateCampaignDonorReport(req, res, next) {
  const { campaignId } = req.params;
  const { startDate, endDate } = req.query;

  if (!campaignId) {
    return res.status(400).json({
      success: false,
      message: "Campaign ID is required",
    });
  }

  // Validate date format if provided
  if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid startDate format. Use YYYY-MM-DD format.",
    });
  }

  if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid endDate format. Use YYYY-MM-DD format.",
    });
  }

  reportsService
    .generateCampaignDonorReport(campaignId, startDate, endDate)
    .then((result) => {
      res.json({
        success: true,
        message: "Campaign donor report generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
          campaignInfo: result.campaignInfo,
          filters: result.filters,
        },
      });
    })
    .catch(next);
}

// Generate Event Volunteer Report (Individual Event)
function generateEventVolunteerReport(req, res, next) {
  const { eventId } = req.params;
  const { startDate, endDate } = req.query;

  if (!eventId) {
    return res.status(400).json({
      success: false,
      message: "Event ID is required",
    });
  }

  // Validate date format if provided
  if (startDate && !/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid startDate format. Use YYYY-MM-DD format.",
    });
  }

  if (endDate && !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
    return res.status(400).json({
      success: false,
      message: "Invalid endDate format. Use YYYY-MM-DD format.",
    });
  }

  reportsService
    .generateEventVolunteerReport(eventId, startDate, endDate)
    .then((result) => {
      res.json({
        success: true,
        message: "Event volunteer report generated successfully",
        data: {
          fileName: result.fileName,
          downloadUrl: result.downloadUrl,
          generatedAt: new Date().toISOString(),
          eventInfo: result.eventInfo,
          filters: result.filters,
        },
      });
    })
    .catch(next);
}
