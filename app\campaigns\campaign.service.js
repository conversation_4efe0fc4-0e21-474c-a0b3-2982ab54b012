﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const { Op, literal, QueryTypes } = require("sequelize");
const { Sequelize } = require("sequelize");
const { format } = require("date-fns");

module.exports = {
  getAll,
  getById,
  create,
  update,
  patch,
  delete: _delete,
  getCampaignsByStatusParam,
  getCampaignsByNgoName,
  getCampaignsByLocationOrCategoryOrSkills,
  getCampaignStats,
  getFullCampaignStats,
  markAsFeatured,
  getCampaigns,
  getUpcomingCampaigns,
  getCampaignsByFilters,
  getByCategoryIds,
  getEventsForUserWithFilters,
  getNgoCampaignStatistics,
  getCampaignStatistics,
  getNgoEventStatistics,
  getEventStatistics,
};
db.Campaign.belongsTo(db.Ngo, {
  as: "ngoInfo",
  through: "ngos",
  foreignKey: "ngo_id",
  otherKey: "ngo_id",
});
db.Campaign.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});
db.KindDonation.belongsTo(db.Category, {
  as: "kindDonation",
  through: "campaign_kinddonations",
  foreignKey: "campaign_id",
  otherKey: "campaign_id",
});

const campaignDescriptions = [
  (name) => `Be a hero for a cause: ${name}`,
  (name) => `Act now, impact lives — ${name}`,
  (name) => `Fuel the mission: ${name}`,
  (name) => `Every contribution counts — support "${name}" today!`,
];

const eventDescriptions = [
  (name) => `You're invited! Be part of ${name}`,
  (name) => `Don't just hear about it — experience ${name}`,
  (name) => `Mark your calendar — ${name} is happening soon!`,
  (name) => `A memorable moment awaits — ${name}`,
];

async function getAll(params) {
  const where = {};
  const { ngoId, status, type } = params;
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  if (status) {
    where.status = status;
  }

  if (type === "Event") {
    where.fund_raising_target = null;
  } else if (type === "Campaign") {
    where.fund_raising_target = { [Op.ne]: null };
  }

  const campaigns = await db.Campaign.findAll({
    order: [["id", "DESC"]],
    where: where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  return campaigns;
}

async function getById(id, params) {
  const record = await getSingleRecord(id);

  if (params?.userId) {
    const isParticipated = await db.CampaignRsvps.findOne({
      where: {
        campaign_id: id,
        user_id: params.userId,
        rsvp_value: "yes",
      },
    });
    record.setDataValue(
      "isParticipated",
      isParticipated?.type === "Participated" ? "yes" : "no"
    );
    record.setDataValue(
      "isScanned",
      isParticipated?.type === "Scanned" ? "yes" : "no"
    );
  }

  // Track campaign view for CTR calculation
  try {
    const today = format(new Date(), "yyyy-MM-dd");

    // Check if record already exists for today
    const existingRecord = await db.DashboardStat.findOne({
      where: {
        type: "campaign",
        typeid: id,
        dateRecorded: today,
      },
    });

    // Only create if doesn't exist
    if (!existingRecord) {
      await db.DashboardStat.create({
        type: "campaign",
        typeid: id,
        dateRecorded: today,
      });
    }
  } catch (error) {
    // Log error but don't fail the main request
    console.error("Error tracking campaign view:", error);
  }

  return record;
}

async function getByCategoryIds(categoryIds) {
  if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
    return [];
  }

  const campaigns = await db.Campaign.findAll({
    where: {
      category_id: {
        [Op.in]: categoryIds,
      },
    },
    order: [["id", "DESC"]],
  });

  return campaigns;
}

async function create(params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }

  const record = await db.Campaign.create(params);
  let matchedUsers = [];
  let matchedUserIds = [];
  try {
    const today = format(new Date(), "yyyy-MM-dd");

    await db.DashboardStat.create({
      type: "campaign_created",
      typeid: record.id,
      dateRecorded: today,
      createdBy: params.createdBy || null,
    });

    if (params.category_id) {
      const categoryId = String(params.category_id);

      const users = await db.User.findAll({
        where: {
          interests: {
            [Op.ne]: null,
          },
        },
      });

      matchedUsers = users.filter((user) => {
        const interestIds = String(user.interests)
          .split(",")
          .map((id) => id.trim());
        return interestIds.includes(categoryId);
      });

      // ✅ Filter users based on notification_settings
      const isCampaign = !!params.fund_raising_target;

      const filteredUsers = matchedUsers.filter((user) => {
        try {
          if (!user.notification_settings) {
            return true;
          }

          const settings = JSON.parse(user.notification_settings || "[]");
          const settingMap = Object.fromEntries(
            settings.map((s) => [s.key, s.value])
          );

          return (
            settingMap["All Notifications"] === true ||
            (isCampaign && settingMap["New Campaigns"] === true) ||
            (!isCampaign && settingMap["Events around you"] === true)
          );
        } catch (err) {
          console.error("Invalid notification_settings for user", user.id);
          return false;
        }
      });

      matchedUserIds = filteredUsers.map((u) => u.id);

      const userTokens = await db.UserToken.findAll({
        where: {
          user_id: matchedUserIds,
        },
        attributes: ["device_token"],
      });

      const deviceTokens = userTokens.map((t) => t.device_token);
      // ✅ Send the actual notification
      const notificationTitle = isCampaign
        ? "📢 A New Campaign Needs Your Support!"
        : "📍 An Exciting Event Near You!";

      const dbSupportedTitle = isCampaign
        ? "A New Campaign Needs Your Support!"
        : "An Exciting Event Near You!";

      const notificationDescription = isCampaign
        ? campaignDescriptions[
            Math.floor(Math.random() * campaignDescriptions.length)
          ](record.name)
        : eventDescriptions[
            Math.floor(Math.random() * eventDescriptions.length)
          ](record.name);
      await utils.sendPushNotification(deviceTokens, {
        title: notificationTitle,
        description: notificationDescription,
      });

      const userNotification = await db.UserNotification.create({
        title: dbSupportedTitle,
        body: notificationDescription,
        type_id: record.id,
        category_id: params.category_id,
        createdBy: params.createdBy || null,
        type: isCampaign ? "campaign" : "event",
      });

      // ✅ Insert UserReadNotification records
      if (matchedUserIds.length > 0) {
        const readRecords = matchedUserIds.map((userId) => ({
          user_id: userId,
          notification_id: userNotification.id,
        }));

        await db.UserReadNotification.bulkCreate(readRecords);
      }
    }
  } catch (error) {
    console.error("Error tracking campaign creation:", error);
  }

  return record;
}

//helper fucntion

async function getCampaignsByFilters(filters, params) {
  const {
    latitude,
    longitude,
    radius = 10,
    ngos,
    causes,
    certificates,
    ngoType,
    search,
    page = 1,
    limit = 10,
    type,
  } = filters;

  const offset = (page - 1) * limit;

  // Step 1: Get base NGO list (as you have it)
  let ngoIds = [];
  if (Array.isArray(ngos) && ngos.length > 0) {
    ngoIds = ngos.map((ngo) => ngo.id);
  } else {
    const verifiedNgos = await db.Ngo.findAll({
      where: { ngo_status: "Verified" },
      attributes: ["id", "grade", "registration_details"],
    });
    ngoIds = verifiedNgos.map((ngo) => ngo.id);
  }

  // Step 2: Apply NGO Type Filter (same as before)
  if (Array.isArray(ngoType) && ngoType.length > 0) {
    const gradeValues = ngoType
      .map((type) => {
        if (type.includes("Gold")) return "Gold";
        if (type.includes("Silver")) return "Silver";
        if (type.includes("Bronze")) return "Bronze";
        return null;
      })
      .filter(Boolean);

    const filteredByType = await db.Ngo.findAll({
      where: {
        id: { [Op.in]: ngoIds },
        grade: { [Op.in]: gradeValues },
      },
      attributes: ["id"],
    });

    ngoIds = filteredByType.map((ngo) => ngo.id);
  }

  // Step 3: Apply Certificate Filter (same as before)
  if (Array.isArray(certificates) && certificates.length > 0) {
    const certConditions = certificates.map((cert) =>
      Sequelize.literal(
        `JSON_UNQUOTE(JSON_EXTRACT(CAST(registration_details AS JSON), '$.${cert}')) IS NOT NULL`
      )
    );

    const certifiedNgos = await db.Ngo.findAll({
      where: {
        id: { [Op.in]: ngoIds },
        [Op.and]: certConditions,
      },
      attributes: ["id"],
    });

    ngoIds = certifiedNgos.map((ngo) => ngo.id);
  }

  // Step 4: Fetch campaigns/events by location FIRST (location is priority)
  // Get all active campaigns/events
  const baseWhereCondition = {
    status: "Active",
  };
  if (type === "Event") {
    baseWhereCondition.fund_raising_target = null; // Events have no fundraising target
  } else if (type === "Campaign") {
    baseWhereCondition.fund_raising_target = { [Op.ne]: null }; // Campaigns must have a fundraising target
  }

  // Fetch campaigns without NGO filtering first, so we can filter by location
  let campaigns = await db.Campaign.findAll({
    where: baseWhereCondition,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name", "grade", "registration_details"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
    limit,
    offset,
    order: [["name", "ASC"]],
  });

  // Filter campaigns by location radius
  if (latitude && longitude) {
    campaigns = campaigns.filter((campaign, index) => {
      const campLat = parseFloat(campaign.latitude);
      const campLng = parseFloat(campaign.longitude);
      if (isNaN(campLat) || isNaN(campLng)) return false;

      const distance = utils.haversineDistance(
        latitude,
        longitude,
        campLat,
        campLng
      );

      return distance <= radius;
    });
  }

  // Step 5: Filter campaigns based on filtered NGO IDs
  campaigns = campaigns.filter((campaign) => ngoIds.includes(campaign.ngo_id));

  // Step 6: Apply remaining filters on campaigns

  // Causes filter
  if (Array.isArray(causes) && causes.length > 0) {
    const causeIds = causes.map((cause) => cause.id);
    campaigns = campaigns.filter((campaign) =>
      causeIds.includes(campaign.category_id)
    );
  }

  // Search filter
  if (search) {
    campaigns = campaigns.filter((campaign) =>
      campaign.name.toLowerCase().includes(search.toLowerCase())
    );
  }

  // Step 7: Paginate final results
  // Sort campaigns alphabetically by name (A-Z) before paginating
  campaigns.sort((a, b) => {
    const nameA = (a.name || "").toLowerCase();
    const nameB = (b.name || "").toLowerCase();
    if (nameA < nameB) return -1;
    if (nameA > nameB) return 1;
    return 0;
  });
  const totalCount = campaigns.length;
  const totalPages = Math.ceil(totalCount / limit);

  return {
    data: campaigns,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
    },
  };
}

function degreesToRadians(degrees) {
  return (degrees * Math.PI) / 180;
}

// async function update(id, params) {
//   const record = await getSingleRecord(id);

//   // copy params to user and save
//   Object.assign(record, params);
//   await record.save();

//   return record.get();
// }
async function update(id, params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }
  const record = await getSingleRecord(id);

  const { inKindList, createdBy } = params;
  const parsedInKindList =
    typeof inKindList === "string" ? JSON.parse(inKindList) : inKindList;

  // Remove the `inKindList` from the params to avoid direct assignment to the Campaign table
  delete params.inKindList;

  // Update campaign fields
  Object.assign(record, params);
  await record.save();

  if (Array.isArray(parsedInKindList)) {
    // Delete existing kind donations for the campaign
    await db.KindDonation.destroy({
      where: {
        campaign_id: id,
      },
    });

    // Add new kind donations if provided
    if (parsedInKindList.length > 0) {
      const kindDonations = parsedInKindList.map((item) => ({
        item_name: item.item,
        quantity: parseInt(item.quantity, 10),
        unit_of_measure: item.unit,
        campaign_id: id,
        createdBy: createdBy,
      }));

      try {
        await db.KindDonation.bulkCreate(kindDonations, {
          validate: true,
        });
      } catch (error) {
        console.error("Error during bulk insert of kind donations:", error);
        throw error;
      }
    }
  }

  // Track campaign update in dashboard stats
  try {
    const { format } = require("date-fns");
    const today = format(new Date(), "yyyy-MM-dd");

    // Check if record already exists for today
    const existingRecord = await db.DashboardStat.findOne({
      where: {
        type: "campaign_updated",
        typeid: id,
        dateRecorded: today,
      },
    });

    // Only create if doesn't exist
    if (!existingRecord) {
      await db.DashboardStat.create({
        type: "campaign_updated",
        typeid: id,
        dateRecorded: today,
        updatedBy: params.updatedBy || null,
      });
    }
  } catch (error) {
    // Log error but don't fail the main request
    console.error("Error tracking campaign update:", error);
  }

  return record.get();
}
async function patch(id, params) {
  if ("name" in params && params.name) {
    await utils.validateTextForAbuse(params.name);
  }

  if ("description" in params && params.description) {
    await utils.validateTextForAbuse(params.description);
  }

  const record = await getSingleRecord(id);
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Campaign.findByPk(id, {
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
    ],
  });
  if (!record) throw "Record not found";

  record.dataValues.fund_raised = 10000;
  return record;
}

async function getCampaignsByStatusParam(
  statusParam,
  ngoId,
  page = 1,
  limit = 50,
  portalUserId,
  searchTerm,
  params
) {
  const { type } = params;
  const offset = (page - 1) * limit;

  let where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }

  let filteredNgoIds = [];

  if (portalUserId) {
    const ngoRecords = await db.Ngo.findAll({
      where: { assignee_id: portalUserId },
      attributes: ["id"],
    });

    filteredNgoIds = ngoRecords.map(({ id }) => id);

    if (filteredNgoIds.length > 0) {
      where.ngo_id = filteredNgoIds;
    } else {
      return {
        result: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        statusCountsMap: {},
      };
    }
  }

  // Add searchTerm filter if provided
  if (searchTerm) {
    where[Op.or] = [{ name: { [Op.like]: `%${searchTerm}%` } }];
  }

  if (statusParam === "campaigns") {
    where.fund_raising_target = { [Op.ne]: null };
  } else {
    where.fund_raising_target = null; // Include only events
  }

  // Get campaigns based on filters with pagination
  const { count, rows } = await db.Campaign.findAndCountAll({
    where: where,
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
  });

  if (!rows || rows.length === 0) {
    return { result: [], totalCount: 0, totalPages: 0, currentPage: page };
  }

  const updatedCampaignsRecord = await Promise.all(
    rows.map(async (campaign) => {
      campaign.fund_raised = 10000;
      return campaign;
    })
  );

  let statusWhereClause = {};
  if (portalUserId) {
    statusWhereClause = { ngo_id: filteredNgoIds };
  } else if (ngoId) {
    statusWhereClause = { ngo_id: ngoId };
  }

  // Get status counts for campaigns
  const statusCounts = await db.Campaign.findAll({
    attributes: [
      "status",
      [Sequelize.fn("COUNT", Sequelize.col("status")), "count"],
    ],
    where: statusWhereClause,
    group: ["status"],
  });

  const statusCountsMap = statusCounts.reduce((acc, item) => {
    if (item.status !== null) {
      acc[item.status] = parseInt(item.dataValues.count, 10);
    }
    return acc;
  }, {});

  if (portalUserId) {
    statusCountsMap["All"] = await db.Campaign.count({
      where: { ngo_id: filteredNgoIds },
    });
  } else if (ngoId) {
    statusCountsMap["All"] = await db.Campaign.count({
      where: { ngo_id: ngoId },
    });
  } else {
    statusCountsMap["All"] = await db.Campaign.count();
  }

  const totalCount = count;
  const totalPages = Math.ceil(totalCount / limit);

  return {
    result: updatedCampaignsRecord,
    totalCount,
    totalPages,
    currentPage: page,
    statusCountsMap,
  };
}

async function getCampaignsByNgoName(ngoName) {
  const result = await db.Campaign.findAll({
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        where: {
          name: {
            [Op.like]: `%${ngoName}%`,
          },
        },
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });

  if (!result.length) return "No campaigns found for the given NGO name";
  return result;
}

async function getCampaignsByLocationOrCategoryOrSkills({
  latitude,
  longitude,
  radius = 10, // default radius in km
  categoryId,
  skills,
}) {
  const where = {};

  // Include category filter if provided
  if (categoryId) {
    where.category_id = categoryId;
  }

  // Skills-based filter (if campaigns or NGOs have a `skills` field)
  if (skills) {
    where.skills = {
      [Op.like]: `%${skills}%`,
    };
  }

  // Get all campaigns and filter manually for location-based distance
  const campaigns = await db.Campaign.findAll({
    where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });

  // Filter by location manually using Haversine formula
  if (latitude && longitude) {
    const EARTH_RADIUS = 6371; // Earth radius in km

    return campaigns.filter((campaign) => {
      const campaignLat = parseFloat(campaign.latitude);
      const campaignLng = parseFloat(campaign.longitude);

      if (isNaN(campaignLat) || isNaN(campaignLng)) return false;

      // Haversine formula
      const dLat = degreesToRadians(campaignLat - latitude);
      const dLng = degreesToRadians(campaignLng - longitude);
      const lat1 = degreesToRadians(latitude);
      const lat2 = degreesToRadians(campaignLat);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.sin(dLng / 2) *
          Math.sin(dLng / 2) *
          Math.cos(lat1) *
          Math.cos(lat2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = EARTH_RADIUS * c; // Distance in km

      return distance <= radius; // Filter campaigns within radius
    });
  }

  return campaigns.length
    ? campaigns
    : "No campaigns found matching the criteria";
}

// Helper function to convert degrees to radians
function degreesToRadians(degrees) {
  return degrees * (Math.PI / 180);
}
async function getCampaignStats(ngoId) {
  const totalCampaigns = await db.Campaign.count({
    where: { ngo_id: ngoId },
  });
  const completed = await db.Campaign.count({
    where: { status: "Completed", ngo_id: ngoId },
  });
  const live = await db.Campaign.count({
    where: { status: "Live", ngo_id: ngoId },
  });
  const approved = await db.Campaign.count({
    where: { status: "Approved", ngo_id: ngoId },
  });
  const inReview = await db.Campaign.count({
    where: { status: "In Review", ngo_id: ngoId },
  });
  const draft = await db.Campaign.count({
    where: { status: "Draft", ngo_id: ngoId },
  });

  return {
    campaignStats: {
      totalCampaigns,
      completed,
      live,
      approved,
      inReview,
      draft,
    },
  };
}
async function getFullCampaignStats() {
  const totalCampaigns = await db.Campaign.count();
  const completed = await db.Campaign.count({
    where: { status: "Completed" },
  });
  const live = await db.Campaign.count({
    where: { status: "Live" },
  });
  const approved = await db.Campaign.count({
    where: { status: "Approved" },
  });
  const inReview = await db.Campaign.count({
    where: { status: "In Review" },
  });
  const draft = await db.Campaign.count({
    where: { status: "Draft" },
  });

  return {
    alertMessage: {
      inReviewCampaginCount: totalCampaigns,
    },

    campaginStats: {
      totalCampaigns: totalCampaigns,
      live: live,
      inReview: inReview,
      approved: approved,
      completed: completed,
    },
  };
}
async function markAsFeatured(campaignId, featureMode) {
  if (!campaignId) {
    throw new Error("Campaign ID is required.");
  }

  const isFeatured = featureMode === "add";
  try {
    await update(campaignId, { isfeatured: isFeatured ? "yes" : "no" });
  } catch (error) {
    throw new Error(`Failed to mark campaign as featured: ${error.message}`);
  }
}

// Service Function
async function getCampaignsByLocationOrCategoryOrSkills(filters) {
  const { latitude, longitude, radius = 10, categoryId, skills } = filters;
  const where = {};

  if (categoryId) {
    where.category_id = categoryId;
  }

  if (skills) {
    where.skills = {
      [Op.like]: `%${skills}%`,
    };
  }

  const campaigns = await db.Campaign.findAll({
    where,
    include: [
      {
        model: db.Ngo,
        as: "ngoInfo",
        attributes: ["id", "name"],
      },
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name"],
      },
    ],
    order: [["id", "DESC"]],
  });

  if (latitude && longitude) {
    const EARTH_RADIUS = 6371;

    return campaigns.filter((campaign) => {
      const campaignLat = parseFloat(campaign.latitude);
      const campaignLng = parseFloat(campaign.longitude);

      if (isNaN(campaignLat) || isNaN(campaignLng)) return false;

      // Haversine formula
      const dLat = degreesToRadians(campaignLat - latitude);
      const dLng = degreesToRadians(campaignLng - longitude);
      const lat1 = degreesToRadians(latitude);
      const lat2 = degreesToRadians(campaignLat);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.sin(dLng / 2) *
          Math.sin(dLng / 2) *
          Math.cos(lat1) *
          Math.cos(lat2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = EARTH_RADIUS * c; // Distance in km

      return distance <= radius;
    });
  }

  return campaigns.length
    ? campaigns
    : "No campaigns found matching the criteria";
}

async function getCampaigns(params) {
  const { search, status, ngo_id, portalUserId, type } = params;

  let filteredNgoIds = [];
  if (portalUserId) {
    const ngoRecords = await db.Ngo.findAll({
      where: { assignee_id: portalUserId },
      attributes: ["id"],
    });

    filteredNgoIds = ngoRecords.map(({ id }) => id);
  }

  const whereClause = {};

  if (search) {
    whereClause.name = {
      [Sequelize.Op.like]: `%${search}%`,
    };
  }

  if (status) {
    whereClause.status = status;
  }
  if (portalUserId) {
    whereClause.ngo_id = { [Sequelize.Op.in]: filteredNgoIds };
  } else if (ngo_id) {
    whereClause.ngo_id = ngo_id;
  }

  if (type == "campaign") {
    whereClause.fund_raising_target = { [Op.ne]: null };
  } else if (type == "event") {
    whereClause.fund_raising_target = null;
  }

  return db.Campaign.findAll({ where: whereClause });
}

async function getUpcomingCampaigns(params) {
  const { type, userId } = params;
  const today = format(new Date(), "yyyy-MM-dd");

  let where = {};
  let order = [];

  if (userId) {
    // 1. Get campaign_ids the user RSVPed 'yes' to
    const rsvps = await db.CampaignRsvps.findAll({
      where: {
        user_id: userId,
        rsvp_value: "yes",
      },
      attributes: ["campaign_id"],
    });

    const campaignIds = rsvps.map((r) => r.campaign_id);
    if (campaignIds.length === 0) return [];

    // 2. Filter campaigns to only these IDs
    where.id = { [Op.in]: campaignIds };

    // 3. Also apply upcoming condition by type
    if (type === "Event") {
      where.fund_raising_target = null;
      where.event_date = { [Op.gt]: today };
      order = [["event_date", "ASC"]];
    } else if (type === "Campaign") {
      where.fund_raising_target = { [Op.ne]: null };
      where.campaign_end_date = { [Op.gt]: today };
      order = [["campaign_end_date", "ASC"]];
    }
  } else {
    // When no userId is passed, return all upcoming campaigns/events
    if (type === "Event") {
      where.fund_raising_target = null;
      where.event_date = { [Op.gt]: today };
      order = [["event_date", "ASC"]];
    } else if (type === "Campaign") {
      where.fund_raising_target = { [Op.ne]: null };
      where.campaign_end_date = { [Op.gt]: today };
      order = [["campaign_end_date", "ASC"]];
    }
  }

  return await db.Campaign.findAll({
    where,
    order,
  });
}

async function getEventsForUserWithFilters({
  userId,
  userLatitude = "18.5124445",
  userLongitude = "73.8389959",
  interests, // '1,3,5'
  duration = null,
  dayType = "anytime",
  page = 1,
  limit = 10,
}) {
  //letUsDecide=yes/no
  //causes=10,11,8,9
  //const achieved = (item.fund_raised ? parseInt(item.fund_raised) : 0) / parseInt(item.fund_raising_target) * 100
  // fund_raised backers
  //rsvp
  let offset = (page - 1) * limit;
  limit = Number(limit);
  offset = Number(offset);

  // 🧭 Fallback: Get user lat/lng and interests if missing
  if (!userLatitude || !userLongitude || !interests) {
    const user = await db.User.findByPk(userId);
    if (!user) throw new Error("User not found");
    if (!userLatitude || !userLongitude) {
      if (!user.latitude || !user.longitude)
        throw new Error("User location missing");
      userLatitude = user.latitude;
      userLongitude = user.longitude;
    }

    if (!interests && user.interests) {
      interests = user.interests;
    }
  }

  userLatitude = parseFloat(userLatitude);
  userLongitude = parseFloat(userLongitude);

  // Helper: Build FIND_IN_SET OR list for interests
  function buildInterestFilter(interests) {
    if (!interests) return null;
    const ids = interests
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean);
    if (ids.length === 0) return null;

    const conditions = ids.map(
      (id) => `FIND_IN_SET(${parseInt(id)}, campaigns.category_id)`
    );
    return `(${conditions.join(" OR ")})`;
  }

  const fallbackLevels = [
    {
      useDistance: true,
      useDuration: true,
      useDay: true,
      useInterest: true,
    },
    {
      useDistance: true,
      useDuration: true,
      useDay: false,
      useInterest: true,
    },
    {
      useDistance: true,
      useDuration: false,
      useDay: false,
      useInterest: true,
    },
    {
      useDistance: false,
      useDuration: false,
      useDay: false,
      useInterest: true,
    },
    {
      useDistance: false,
      useDuration: false,
      useDay: false,
      useInterest: false,
    },
  ];
  // const fallbackLevels = [
  //   { useDistance: true, useDuration: false, useDay: false, useInterest: false }
  // ];
  function buildEventFilterSQL({
    useDistance,
    useDuration,
    useDay,
    useInterest,
  }) {
    const conditions = [];

    if (useDistance) {
      conditions.push(`(
        6371 * acos(
          cos(radians(:user_lat)) * cos(radians(campaigns.latitude)) *
          cos(radians(campaigns.longitude) - radians(:user_lng)) +
          sin(radians(:user_lat)) * sin(radians(campaigns.latitude))
        )
      ) <= 150`);
    }

    if (useDuration) {
      conditions.push(`(
        (:duration = '30m-1h' AND TIMESTAMPDIFF(MINUTE, event_start_time, event_end_time) BETWEEN 30 AND 60)
        OR (:duration = '1h-2h' AND TIMESTAMPDIFF(MINUTE, event_start_time, event_end_time) > 60 AND TIMESTAMPDIFF(MINUTE, event_start_time, event_end_time) <= 120)
        OR (:duration = '2h-4h' AND TIMESTAMPDIFF(MINUTE, event_start_time, event_end_time) > 120 AND TIMESTAMPDIFF(MINUTE, event_start_time, event_end_time) <= 240)
        OR (:duration = 'full_day' AND TIMESTAMPDIFF(MINUTE, event_start_time, event_end_time) > 240)
        OR (:duration IS NULL)
      )`);
    }

    if (useDay) {
      conditions.push(`(
        :day_type = 'anytime'
        OR (:day_type = 'weekend' AND DAYOFWEEK(event_date) IN (1, 7))
        OR (:day_type = 'weekday' AND DAYOFWEEK(event_date) BETWEEN 2 AND 6)
      )`);
    }

    if (useInterest) {
      const interestClause = buildInterestFilter(interests);
      if (interestClause) {
        conditions.push(interestClause);
      }
    }

    return conditions.length ? "AND " + conditions.join(" AND ") : "";
  }

  let fallbackLevelUsed = 0;
  let events = [];
  let totalCount = 0;

  for (let i = 0; i < fallbackLevels.length; i++) {
    const filterClause = buildEventFilterSQL(fallbackLevels[i]);

    // 🔍 Main Query
    const sql = `
      SELECT *,
        (
          6371 * acos(
            cos(radians(:user_lat)) * cos(radians(campaigns.latitude)) *
            cos(radians(campaigns.longitude) - radians(:user_lng)) +
            sin(radians(:user_lat)) * sin(radians(campaigns.latitude))
          )
        ) AS distance_km
      FROM campaigns
      WHERE sameday_event = 'yes'
        AND (
          event_date > CURDATE()
          OR (event_date = CURDATE() AND event_end_time > CURTIME())
        )
        ${filterClause}
      ORDER BY event_date ASC, event_start_time ASC
      LIMIT :limit OFFSET :offset
    `;

    const countSql = `
      SELECT COUNT(*) as count
      FROM campaigns
      WHERE sameday_event = 'yes'
        AND (
          event_date > CURDATE()
          OR (event_date = CURDATE() AND event_end_time > CURTIME())
        )
        ${filterClause}
    `;

    const replacements = {
      user_lat: userLatitude,
      user_lng: userLongitude,
      duration,
      day_type: dayType,
      limit,
      offset,
    };

    const result = await db.sequelize.query(sql, {
      replacements,
      type: QueryTypes.SELECT,
    });

    const countResult = await db.sequelize.query(countSql, {
      replacements,
      type: QueryTypes.SELECT,
    });

    if (result.length > 0) {
      fallbackLevelUsed = i + 1;
      events = result;
      totalCount = countResult[0].count;
      break;
    }
  }

  return {
    events,
    totalCount,
    fallbackLevel: fallbackLevelUsed,
  };
}

async function getNgoCampaignStatistics(ngoId = null) {
  try {
    // Build where conditions based on whether ngoId is provided
    const campaignWhereCondition = ngoId
      ? { ngo_id: ngoId, fund_raising_target: { [Op.ne]: null } }
      : { fund_raising_target: { [Op.ne]: null } };

    const transactionWhereCondition = ngoId
      ? { ngo_id: ngoId, donation_type: "campaign", status: "captured" }
      : { donation_type: "campaign", status: "captured" };

    // Helper function to get total fund goal
    const getTotalFundGoal = async () => {
      const result = await db.Campaign.findOne({
        where: campaignWhereCondition,
        attributes: [
          [
            Sequelize.fn("SUM", Sequelize.col("fund_raising_target")),
            "totalFundGoal",
          ],
        ],
      });
      return parseFloat(result?.dataValues?.totalFundGoal || 0);
    };

    // Helper function to get lives impacted count
    const getLivesImpacted = async () => {
      const result = await db.Transaction.findOne({
        where: transactionWhereCondition,
        attributes: [
          [
            Sequelize.fn("SUM", Sequelize.col("impact_created")),
            "totalLivesImpacted",
          ],
        ],
      });
      return parseInt(result?.dataValues?.totalLivesImpacted || 0);
    };

    // Helper function to get fund collected amount and percentage
    const getFundCollected = async () => {
      const result = await db.Campaign.findOne({
        where: campaignWhereCondition,
        attributes: [
          [
            Sequelize.fn("SUM", Sequelize.col("amount_raised")),
            "totalFundCollected",
          ],
          [
            Sequelize.fn("AVG", Sequelize.col("goal_percentage_achieved")),
            "avgPercentageAchieved",
          ],
        ],
      });
      return {
        totalFundCollected: parseFloat(
          result?.dataValues?.totalFundCollected || 0
        ),
        avgPercentageAchieved: parseFloat(
          result?.dataValues?.avgPercentageAchieved || 0
        ),
      };
    };

    // Helper function to get lives impacted breakdown by category
    const getLivesImpactedBreakdown = async () => {
      const whereClause = ngoId
        ? "WHERE t.ngo_id = :ngoId AND t.donation_type = 'campaign' AND t.status = 'captured' AND camp.fund_raising_target IS NOT NULL"
        : "WHERE t.donation_type = 'campaign' AND t.status = 'captured' AND camp.fund_raising_target IS NOT NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          c.name as categoryName,
          SUM(t.impact_created) as livesImpacted,
          COUNT(t.id) as transactionCount
        FROM transactions t
        JOIN campaigns camp ON t.campaign_id = camp.id
        JOIN categories c ON camp.category_id = c.id
        ${whereClause}
        GROUP BY c.id, c.name
        ORDER BY livesImpacted DESC
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      const totalLives = result.reduce(
        (sum, item) => sum + parseInt(item.livesImpacted || 0),
        0
      );

      return result.map((item) => ({
        categoryName: item.categoryName,
        livesImpacted: parseInt(item.livesImpacted || 0),
        percentage:
          totalLives > 0
            ? ((parseInt(item.livesImpacted || 0) / totalLives) * 100).toFixed(
                2
              )
            : 0,
        transactionCount: parseInt(item.transactionCount || 0),
      }));
    };

    // Helper function to get top donors with detailed donation data
    const getTopDonors = async () => {
      const whereClause = ngoId
        ? "WHERE t.ngo_id = :ngoId AND t.donation_type = 'campaign' AND t.status = 'captured'"
        : "WHERE t.donation_type = 'campaign' AND t.status = 'captured'";

      const result = await db.sequelize.query(
        `
        SELECT
          u.id as user_id,
          u.fullname,
          u.email,
          SUM(t.amount) as totalDonated,
          COUNT(t.id) as donationCount,
          MAX(t.createdAt) as lastDonationDate
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        ${whereClause}
        GROUP BY t.user_id, u.fullname, u.email
        ORDER BY totalDonated DESC
        LIMIT 10
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      // Get detailed donation data for each top donor
      const topDonorsWithDetails = await Promise.all(
        result.map(async (item) => {
          const donationDetailsWhereClause = ngoId
            ? "WHERE t.ngo_id = :ngoId AND t.donation_type = 'campaign' AND t.status = 'captured' AND t.user_id = :userId"
            : "WHERE t.donation_type = 'campaign' AND t.status = 'captured' AND t.user_id = :userId";

          const donationDetails = await db.sequelize.query(
            `
            SELECT
              t.id as transaction_id,
              t.amount,
              t.createdAt as donation_date,
              t.razorpay_payment_id,
              t.impact_created,
              c.name as campaign_name,
              c.id as campaign_id
            FROM transactions t
            LEFT JOIN campaigns c ON t.campaign_id = c.id
            ${donationDetailsWhereClause}
            ORDER BY t.createdAt DESC
            `,
            {
              replacements: ngoId
                ? { ngoId, userId: item.user_id }
                : { userId: item.user_id },
              type: QueryTypes.SELECT,
            }
          );

          return {
            donorName: item.fullname || "Anonymous",
            email: item.email,
            userId: item.user_id,
            totalDonated: parseFloat(item.totalDonated || 0),
            donationCount: parseInt(item.donationCount || 0),
            lastDonationDate: item.lastDonationDate,
            donationDetails: donationDetails.map((detail) => ({
              transactionId: detail.transaction_id,
              amount: parseFloat(detail.amount || 0),
              donationDate: detail.donation_date,
              paymentId: detail.razorpay_payment_id,
              impactCreated: parseInt(detail.impact_created || 0),
              campaignName: detail.campaign_name,
              campaignId: detail.campaign_id,
            })),
          };
        })
      );

      return topDonorsWithDetails;
    };

    // Helper function to get fund collection trend (month-wise for different periods)
    const getFundCollectionTrend = async () => {
      const whereClause = ngoId
        ? "WHERE t.ngo_id = :ngoId AND t.donation_type = 'campaign' AND t.status = 'captured'"
        : "WHERE t.donation_type = 'campaign' AND t.status = 'captured'";

      // Get 3 months data
      const result3M = await db.sequelize.query(
        `
        SELECT
          DATE_FORMAT(t.createdAt, '%Y-%m') as month,
          DATE_FORMAT(t.createdAt, '%Y-%m-%d') as date,
          SUM(t.amount) as totalAmount,
          COUNT(t.id) as transactionCount
        FROM transactions t
        ${whereClause}
          AND t.createdAt >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
        GROUP BY DATE_FORMAT(t.createdAt, '%Y-%m')
        ORDER BY month ASC
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      // Get 6 months data
      const result6M = await db.sequelize.query(
        `
        SELECT
          DATE_FORMAT(t.createdAt, '%Y-%m') as month,
          DATE_FORMAT(t.createdAt, '%Y-%m-%d') as date,
          SUM(t.amount) as totalAmount,
          COUNT(t.id) as transactionCount
        FROM transactions t
        ${whereClause}
          AND t.createdAt >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(t.createdAt, '%Y-%m')
        ORDER BY month ASC
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      // Get 1 year data
      const result1Y = await db.sequelize.query(
        `
        SELECT
          DATE_FORMAT(t.createdAt, '%Y-%m') as month,
          DATE_FORMAT(t.createdAt, '%Y-%m-%d') as date,
          SUM(t.amount) as totalAmount,
          COUNT(t.id) as transactionCount
        FROM transactions t
        ${whereClause}
          AND t.createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(t.createdAt, '%Y-%m')
        ORDER BY month ASC
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      return {
        "3M": result3M.map((item) => ({
          month: item.month,
          date: item.date,
          amount: parseFloat(item.totalAmount || 0),
          transactionCount: parseInt(item.transactionCount || 0),
        })),
        "6M": result6M.map((item) => ({
          month: item.month,
          date: item.date,
          amount: parseFloat(item.totalAmount || 0),
          transactionCount: parseInt(item.transactionCount || 0),
        })),
        "1Y": result1Y.map((item) => ({
          month: item.month,
          date: item.date,
          amount: parseFloat(item.totalAmount || 0),
          transactionCount: parseInt(item.transactionCount || 0),
        })),
      };
    };

    // Helper function to get campaign counts
    const getCampaignCounts = async () => {
      const totalCampaigns = await db.Campaign.count({
        where: campaignWhereCondition,
      });

      const activeCampaignWhereCondition = ngoId
        ? {
            ngo_id: ngoId,
            fund_raising_target: { [Op.ne]: null },
            campaign_end_date: { [Op.gt]: new Date() },
          }
        : {
            fund_raising_target: { [Op.ne]: null },
            campaign_end_date: { [Op.gt]: new Date() },
          };

      const activeCampaigns = await db.Campaign.count({
        where: activeCampaignWhereCondition,
      });

      return {
        totalCampaigns,
        activeCampaigns,
      };
    };

    // Helper function to get campaign list with performance metrics
    const getCampaignList = async () => {
      const campaigns = await db.Campaign.findAll({
        where: campaignWhereCondition,
        include: [
          {
            model: db.Category,
            as: "categoryInfo",
            attributes: ["id", "name"],
          },
        ],
        order: [["amount_raised", "DESC"]],
        limit: 20,
      });

      // Get lives impacted for each campaign
      const campaignStats = await Promise.all(
        campaigns.map(async (campaign) => {
          const livesImpactedResult = await db.Transaction.findOne({
            where: {
              campaign_id: campaign.id,
              donation_type: "campaign",
              status: "captured",
            },
            attributes: [
              [
                Sequelize.fn("SUM", Sequelize.col("impact_created")),
                "livesImpacted",
              ],
            ],
          });

          const livesImpacted = parseInt(
            livesImpactedResult?.dataValues?.livesImpacted || 0
          );
          const fundGoal = parseFloat(campaign.fund_raising_target || 0);
          const fundReceived = parseFloat(campaign.amount_raised || 0);
          const percentageAchieved =
            fundGoal > 0 ? ((fundReceived / fundGoal) * 100).toFixed(2) : 0;

          return {
            id: campaign.id,
            name: campaign.name,
            category: campaign.categoryInfo?.name || "N/A",
            status: campaign.status,
            fundGoal,
            fundReceived,
            percentageAchieved: parseFloat(percentageAchieved),
            livesImpacted,
            createdAt: campaign.createdAt,
            campaignEndDate: campaign.campaign_end_date,
          };
        })
      );

      return campaignStats;
    };

    // Execute all helper functions
    const [
      totalFundGoal,
      livesImpacted,
      fundCollected,
      livesImpactedBreakdown,
      topDonors,
      fundCollectionTrend,
      campaignCounts,
      campaignList,
    ] = await Promise.all([
      getTotalFundGoal(),
      getLivesImpacted(),
      getFundCollected(),
      getLivesImpactedBreakdown(),
      getTopDonors(),
      getFundCollectionTrend(),
      getCampaignCounts(),
      getCampaignList(),
    ]);

    return {
      success: true,
      data: {
        // 1. Total Fund Goal
        totalFundGoal,

        // 2. Lives Impacted Count
        livesImpacted,

        // 3. Fund Collected Amount + Percentage
        fundCollected: {
          amount: fundCollected.totalFundCollected,
          percentage: fundCollected.avgPercentageAchieved,
        },

        // 4. Lives Impacted Breakdown
        livesImpactedBreakdown,

        // 6. Top Donors
        topDonors,

        // 7. Fund Collection Trend
        fundCollectionTrend,

        // 8. Total Campaigns
        totalCampaigns: campaignCounts.totalCampaigns,

        // 9. Active Campaigns Count
        activeCampaigns: campaignCounts.activeCampaigns,

        // 10. Campaign List with Performance Metrics
        campaignList,
      },
    };
  } catch (error) {
    console.error("Error in getNgoCampaignStatistics:", error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

// Single Campaign Statistics API
async function getCampaignStatistics(campaignId) {
  try {
    // First, verify the campaign exists and is a fundraising campaign
    const campaign = await db.Campaign.findOne({
      where: {
        id: campaignId,
        fund_raising_target: { [Op.ne]: null },
      },
      include: [
        {
          model: db.Category,
          as: "categoryInfo",
          attributes: ["id", "name"],
        },
      ],
    });

    if (!campaign) {
      return {
        success: false,
        error: "Campaign not found or is not a fundraising campaign",
        data: null,
      };
    }

    // Helper function to get total fund goal for this campaign
    const getTotalFundGoal = async () => {
      return parseFloat(campaign.fund_raising_target || 0);
    };

    // Helper function to get lives impacted count for this campaign
    const getLivesImpacted = async () => {
      const result = await db.Transaction.findOne({
        where: {
          campaign_id: campaignId,
          donation_type: "campaign",
          status: "captured",
        },
        attributes: [
          [
            Sequelize.fn("SUM", Sequelize.col("impact_created")),
            "totalLivesImpacted",
          ],
        ],
      });
      return parseInt(result?.dataValues?.totalLivesImpacted || 0);
    };

    // Helper function to get fund collected amount and percentage for this campaign
    const getFundCollected = async () => {
      const fundGoal = parseFloat(campaign.fund_raising_target || 0);
      const fundReceived = parseFloat(campaign.amount_raised || 0);
      const percentageAchieved =
        fundGoal > 0 ? ((fundReceived / fundGoal) * 100).toFixed(2) : 0;

      return {
        totalFundCollected: fundReceived,
        percentageAchieved: parseFloat(percentageAchieved),
      };
    };

    // Helper function to get lives impacted breakdown by category (for this campaign)
    const getLivesImpactedBreakdown = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          c.name as categoryName,
          SUM(t.impact_created) as livesImpacted,
          COUNT(t.id) as transactionCount
        FROM transactions t
        JOIN campaigns camp ON t.campaign_id = camp.id
        JOIN categories c ON camp.category_id = c.id
        WHERE t.campaign_id = :campaignId
          AND t.donation_type = 'campaign'
          AND t.status = 'captured'
        GROUP BY c.id, c.name
      `,
        {
          replacements: { campaignId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((item) => ({
        categoryName: item.categoryName,
        livesImpacted: parseInt(item.livesImpacted || 0),
        percentage: 100, // Since it's a single campaign, it's 100% of its own impact
        transactionCount: parseInt(item.transactionCount || 0),
      }));
    };

    // Helper function to get top donors for this campaign with detailed donation data
    const getTopDonors = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          u.id as user_id,
          u.fullname,
          u.email,
          SUM(t.amount) as totalDonated,
          COUNT(t.id) as donationCount,
          MAX(t.createdAt) as lastDonationDate
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        WHERE t.campaign_id = :campaignId
          AND t.donation_type = 'campaign'
          AND t.status = 'captured'
        GROUP BY t.user_id, u.fullname, u.email
        ORDER BY totalDonated DESC
        LIMIT 10
      `,
        {
          replacements: { campaignId },
          type: QueryTypes.SELECT,
        }
      );

      // Get detailed donation data for each top donor for this campaign
      const topDonorsWithDetails = await Promise.all(
        result.map(async (item) => {
          const donationDetails = await db.sequelize.query(
            `
            SELECT
              t.id as transaction_id,
              t.amount,
              t.createdAt as donation_date,
              t.razorpay_payment_id,
              t.impact_created
            FROM transactions t
            WHERE t.campaign_id = :campaignId
              AND t.donation_type = 'campaign'
              AND t.status = 'captured'
              AND t.user_id = :userId
            ORDER BY t.createdAt DESC
            `,
            {
              replacements: { campaignId, userId: item.user_id },
              type: QueryTypes.SELECT,
            }
          );

          return {
            donorName: item.fullname || "Anonymous",
            email: item.email,
            userId: item.user_id,
            totalDonated: parseFloat(item.totalDonated || 0),
            donationCount: parseInt(item.donationCount || 0),
            lastDonationDate: item.lastDonationDate,
            donationDetails: donationDetails.map((detail) => ({
              transactionId: detail.transaction_id,
              amount: parseFloat(detail.amount || 0),
              donationDate: detail.donation_date,
              paymentId: detail.razorpay_payment_id,
              impactCreated: parseInt(detail.impact_created || 0),
            })),
          };
        })
      );

      return topDonorsWithDetails;
    };

    // Helper function to get fund collection trend for this campaign (different periods)
    const getFundCollectionTrend = async () => {
      // Get 3 months data
      const result3M = await db.sequelize.query(
        `
        SELECT
          DATE_FORMAT(t.createdAt, '%Y-%m') as month,
          DATE_FORMAT(t.createdAt, '%Y-%m-%d') as date,
          SUM(t.amount) as totalAmount,
          COUNT(t.id) as transactionCount
        FROM transactions t
        WHERE t.campaign_id = :campaignId
          AND t.donation_type = 'campaign'
          AND t.status = 'captured'
          AND t.createdAt >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
        GROUP BY DATE_FORMAT(t.createdAt, '%Y-%m')
        ORDER BY month ASC
      `,
        {
          replacements: { campaignId },
          type: QueryTypes.SELECT,
        }
      );

      // Get 6 months data
      const result6M = await db.sequelize.query(
        `
        SELECT
          DATE_FORMAT(t.createdAt, '%Y-%m') as month,
          DATE_FORMAT(t.createdAt, '%Y-%m-%d') as date,
          SUM(t.amount) as totalAmount,
          COUNT(t.id) as transactionCount
        FROM transactions t
        WHERE t.campaign_id = :campaignId
          AND t.donation_type = 'campaign'
          AND t.status = 'captured'
          AND t.createdAt >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(t.createdAt, '%Y-%m')
        ORDER BY month ASC
      `,
        {
          replacements: { campaignId },
          type: QueryTypes.SELECT,
        }
      );

      // Get 1 year data
      const result1Y = await db.sequelize.query(
        `
        SELECT
          DATE_FORMAT(t.createdAt, '%Y-%m') as month,
          DATE_FORMAT(t.createdAt, '%Y-%m-%d') as date,
          SUM(t.amount) as totalAmount,
          COUNT(t.id) as transactionCount
        FROM transactions t
        WHERE t.campaign_id = :campaignId
          AND t.donation_type = 'campaign'
          AND t.status = 'captured'
          AND t.createdAt >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(t.createdAt, '%Y-%m')
        ORDER BY month ASC
      `,
        {
          replacements: { campaignId },
          type: QueryTypes.SELECT,
        }
      );

      return {
        "3M": result3M.map((item) => ({
          month: item.month,
          date: item.date,
          amount: parseFloat(item.totalAmount || 0),
          transactionCount: parseInt(item.transactionCount || 0),
        })),
        "6M": result6M.map((item) => ({
          month: item.month,
          date: item.date,
          amount: parseFloat(item.totalAmount || 0),
          transactionCount: parseInt(item.transactionCount || 0),
        })),
        "1Y": result1Y.map((item) => ({
          month: item.month,
          date: item.date,
          amount: parseFloat(item.totalAmount || 0),
          transactionCount: parseInt(item.transactionCount || 0),
        })),
      };
    };

    // Execute all helper functions
    const [
      totalFundGoal,
      livesImpacted,
      fundCollected,
      livesImpactedBreakdown,
      topDonors,
      fundCollectionTrend,
    ] = await Promise.all([
      getTotalFundGoal(),
      getLivesImpacted(),
      getFundCollected(),
      getLivesImpactedBreakdown(),
      getTopDonors(),
      getFundCollectionTrend(),
    ]);

    return {
      success: true,
      data: {
        // Campaign basic info
        campaignInfo: {
          id: campaign.id,
          name: campaign.name,
          category: campaign.categoryInfo?.name || "N/A",
          status: campaign.status,
          createdAt: campaign.createdAt,
          campaignEndDate: campaign.campaign_end_date,
        },

        // 1. Total Fund Goal
        totalFundGoal,

        // 2. Lives Impacted Count
        livesImpacted,

        // 3. Fund Collected Amount + Percentage
        fundCollected: {
          amount: fundCollected.totalFundCollected,
          percentage: fundCollected.percentageAchieved,
        },

        // 4. Lives Impacted Breakdown
        livesImpactedBreakdown,

        // 5. Top Donors
        topDonors,

        // 6. Fund Collection Trend
        fundCollectionTrend,
      },
    };
  } catch (error) {
    console.error("Error in getCampaignStatistics:", error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

// Comprehensive NGO Event Statistics API
async function getNgoEventStatistics(ngoId = null) {
  try {
    // Build where conditions based on whether ngoId is provided
    const eventWhereClause = ngoId
      ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL"
      : "WHERE c.fund_raising_target IS NULL";

    // Helper function to get total volunteer hours
    const getTotalVolunteerHours = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          SUM(cr.actualHours) as totalVolunteerHours
        FROM campaign_rsvps cr
        JOIN campaigns c ON cr.campaign_id = c.id
        ${eventWhereClause}
          AND cr.actualHours IS NOT NULL
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );
      return parseInt(result[0]?.totalVolunteerHours || 0);
    };

    // Helper function to get location data for heatmap (physical events only)
    const getLocationData = async () => {
      const locationWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL AND c.event_type = 'physical' AND c.latitude IS NOT NULL AND c.longitude IS NOT NULL"
        : "WHERE c.fund_raising_target IS NULL AND c.event_type = 'physical' AND c.latitude IS NOT NULL AND c.longitude IS NOT NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          c.id,
          c.name,
          c.latitude,
          c.longitude,
          c.place_name,
          c.state,
          COUNT(cr.id) as rsvpCount,
          SUM(CASE WHEN cr.type = 'Scanned' THEN 1 ELSE 0 END) as actualParticipants
        FROM campaigns c
        LEFT JOIN campaign_rsvps cr ON c.id = cr.campaign_id AND cr.rsvp_value = 'yes'
        ${locationWhereClause}
        GROUP BY c.id, c.name, c.latitude, c.longitude, c.place_name, c.state
        ORDER BY rsvpCount DESC
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      return result.map((item) => ({
        eventId: item.id,
        eventName: item.name,
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        placeName: item.place_name,
        city: item.city,
        state: item.state,
        rsvpCount: parseInt(item.rsvpCount || 0),
        actualParticipants: parseInt(item.actualParticipants || 0),
      }));
    };

    // Helper function to get demographic participation data
    const getDemographicData = async () => {
      const demographicWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL AND cr.rsvp_value = 'yes' AND u.gender IS NOT NULL"
        : "WHERE c.fund_raising_target IS NULL AND cr.rsvp_value = 'yes' AND u.gender IS NOT NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          u.gender,
          COUNT(cr.id) as participantCount
        FROM campaign_rsvps cr
        JOIN campaigns c ON cr.campaign_id = c.id
        JOIN users u ON cr.user_id = u.id
        ${demographicWhereClause}
        GROUP BY u.gender
        ORDER BY participantCount DESC
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      const totalParticipants = result.reduce(
        (sum, item) => sum + parseInt(item.participantCount || 0),
        0
      );

      return result.map((item) => ({
        gender: item.gender,
        count: parseInt(item.participantCount || 0),
        percentage:
          totalParticipants > 0
            ? (
                (parseInt(item.participantCount || 0) / totalParticipants) *
                100
              ).toFixed(2)
            : 0,
      }));
    };

    // Helper function to get RSVP statistics
    const getRsvpStatistics = async () => {
      const rsvpWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL"
        : "WHERE c.fund_raising_target IS NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          COUNT(CASE WHEN cr.rsvp_value = 'yes' THEN 1 END) as rsvpYesCount,
          COUNT(CASE WHEN cr.rsvp_value = 'no' THEN 1 END) as rsvpNoCount,
          COUNT(CASE WHEN cr.type = 'Scanned' THEN 1 END) as actualParticipants,
          COUNT(CASE WHEN cr.rsvp_value = 'yes' AND cr.type = 'Participated' THEN 1 END) as acknowledgedButNoShow
        FROM campaign_rsvps cr
        JOIN campaigns c ON cr.campaign_id = c.id
        ${rsvpWhereClause}
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      const stats = result[0];
      const rsvpYesCount = parseInt(stats.rsvpYesCount || 0);
      const actualParticipants = parseInt(stats.actualParticipants || 0);
      const conversionRate =
        rsvpYesCount > 0
          ? ((actualParticipants / rsvpYesCount) * 100).toFixed(2)
          : 0;

      return {
        rsvpYesCount,
        rsvpNoCount: parseInt(stats.rsvpNoCount || 0),
        actualParticipants,
        acknowledgedButNoShow: parseInt(stats.acknowledgedButNoShow || 0),
        conversionRate: parseFloat(conversionRate),
      };
    };

    // Helper function to get total impact hours delivered
    const getTotalImpactHours = async () => {
      const impactWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL AND cr.rsvp_value = 'yes' AND cr.type = 'Scanned' AND cr.impactHours IS NOT NULL"
        : "WHERE c.fund_raising_target IS NULL AND cr.rsvp_value = 'yes' AND cr.type = 'Scanned' AND cr.impactHours IS NOT NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          SUM(cr.impactHours) as totalImpactHours
        FROM campaign_rsvps cr
        JOIN campaigns c ON cr.campaign_id = c.id
        ${impactWhereClause}
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );
      return parseInt(result[0]?.totalImpactHours || 0);
    };

    // Helper function to get conversion funnel data
    const getConversionFunnel = async () => {
      const funnelWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL"
        : "WHERE c.fund_raising_target IS NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          COUNT(DISTINCT c.id) as totalEvents,
          COUNT(cr.id) as totalInvited,
          COUNT(CASE WHEN cr.rsvp_value = 'yes' THEN 1 END) as rsvpYes,
          COUNT(CASE WHEN cr.type = 'Scanned' THEN 1 END) as participated,
          SUM(CASE WHEN cr.type = 'Scanned' AND cr.impactHours IS NOT NULL THEN cr.impactHours ELSE 0 END) as impactDelivered
        FROM campaigns c
        LEFT JOIN campaign_rsvps cr ON c.id = cr.campaign_id
        ${funnelWhereClause}
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      const stats = result[0];
      return {
        totalEvents: parseInt(stats.totalEvents || 0),
        totalInvited: parseInt(stats.totalInvited || 0),
        rsvpYes: parseInt(stats.rsvpYes || 0),
        participated: parseInt(stats.participated || 0),
        impactDelivered: parseInt(stats.impactDelivered || 0),
      };
    };

    // Helper function to get top volunteers with detailed participation data
    const getTopVolunteers = async () => {
      const volunteersWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL AND cr.type = 'Scanned' AND cr.actualHours IS NOT NULL"
        : "WHERE c.fund_raising_target IS NULL AND cr.type = 'Scanned' AND cr.actualHours IS NOT NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          u.id as user_id,
          u.fullname,
          u.email,
          SUM(cr.actualHours) as totalVolunteerHours,
          COUNT(cr.id) as eventsParticipated,
          MAX(cr.createdAt) as lastParticipationDate
        FROM campaign_rsvps cr
        JOIN campaigns c ON cr.campaign_id = c.id
        JOIN users u ON cr.user_id = u.id
        ${volunteersWhereClause}
        GROUP BY cr.user_id, u.fullname, u.email
        ORDER BY totalVolunteerHours DESC
        LIMIT 10
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      // Get detailed participation data for each top volunteer
      const topVolunteersWithDetails = await Promise.all(
        result.map(async (item) => {
          const participationDetailsWhereClause = ngoId
            ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL AND cr.type = 'Scanned' AND cr.user_id = :userId"
            : "WHERE c.fund_raising_target IS NULL AND cr.type = 'Scanned' AND cr.user_id = :userId";

          const participationDetails = await db.sequelize.query(
            `
            SELECT
              cr.id as rsvp_id,
              cr.actualHours,
              cr.impactHours,
              cr.createdAt as participation_date,
              c.name as event_name,
              c.id as event_id,
              c.event_date,
              c.place_name,
              c.state
            FROM campaign_rsvps cr
            JOIN campaigns c ON cr.campaign_id = c.id
            ${participationDetailsWhereClause}
            ORDER BY cr.createdAt DESC
            `,
            {
              replacements: ngoId
                ? { ngoId, userId: item.user_id }
                : { userId: item.user_id },
              type: QueryTypes.SELECT,
            }
          );

          return {
            volunteerName: item.fullname || "Anonymous",
            email: item.email,
            userId: item.user_id,
            totalVolunteerHours: parseFloat(item.totalVolunteerHours || 0),
            eventsParticipated: parseInt(item.eventsParticipated || 0),
            lastParticipationDate: item.lastParticipationDate,
            participationDetails: participationDetails.map((detail) => ({
              rsvpId: detail.rsvp_id,
              actualHours: parseFloat(detail.actualHours || 0),
              impactHours: parseFloat(detail.impactHours || 0),
              participationDate: detail.participation_date,
              eventName: detail.event_name,
              eventId: detail.event_id,
              eventDate: detail.event_date,
              location: `${detail.place_name || ""}, ${
                detail.state || ""
              }`.replace(/^,\s*|,\s*$/g, ""),
            })),
          };
        })
      );

      return topVolunteersWithDetails;
    };

    // Helper function to get top 20 events by RSVP yes and scanned with detailed RSVP data
    const getTopEvents = async () => {
      const topEventsWhereClause = ngoId
        ? "WHERE c.ngo_id = :ngoId AND c.fund_raising_target IS NULL"
        : "WHERE c.fund_raising_target IS NULL";

      const result = await db.sequelize.query(
        `
        SELECT
          c.id,
          c.name,
          c.event_type,
          c.event_date,
          c.place_name,
          c.state,
          COUNT(CASE WHEN cr.rsvp_value = 'yes' THEN 1 END) as rsvpYesCount,
          COUNT(CASE WHEN cr.rsvp_value = 'no' THEN 1 END) as rsvpNoCount,
          COUNT(CASE WHEN cr.type = 'Scanned' THEN 1 END) as scannedCount,
          SUM(CASE WHEN cr.type = 'Scanned' AND cr.impactHours IS NOT NULL THEN cr.impactHours ELSE 0 END) as totalImpactHours
        FROM campaigns c
        LEFT JOIN campaign_rsvps cr ON c.id = cr.campaign_id
        ${topEventsWhereClause}
        GROUP BY c.id, c.name, c.event_type, c.event_date, c.place_name,  c.state
        ORDER BY rsvpYesCount DESC, scannedCount DESC
        LIMIT 20
      `,
        {
          replacements: ngoId ? { ngoId } : {},
          type: QueryTypes.SELECT,
        }
      );

      // Get detailed RSVP data for each top event
      const topEventsWithDetails = await Promise.all(
        result.map(async (item) => {
          const rsvpDetailsWhereClause = ngoId
            ? "WHERE c.ngo_id = :ngoId AND cr.campaign_id = :eventId"
            : "WHERE cr.campaign_id = :eventId";

          const rsvpDetails = await db.sequelize.query(
            `
            SELECT
              cr.id as rsvp_id,
              cr.rsvp_value,
              cr.type,
              cr.actualHours,
              cr.impactHours,
              cr.createdAt as rsvp_date,
              u.fullname as volunteer_name,
              u.email as volunteer_email,
              u.id as user_id
            FROM campaign_rsvps cr
            JOIN campaigns c ON cr.campaign_id = c.id
            JOIN users u ON cr.user_id = u.id
            ${rsvpDetailsWhereClause}
            ORDER BY cr.createdAt DESC
            `,
            {
              replacements: ngoId
                ? { ngoId, eventId: item.id }
                : { eventId: item.id },
              type: QueryTypes.SELECT,
            }
          );

          const rsvpYesCount = parseInt(item.rsvpYesCount || 0);
          const rsvpNoCount = parseInt(item.rsvpNoCount || 0);
          const scannedCount = parseInt(item.scannedCount || 0);
          const acknowledgedButNoShow = rsvpYesCount - scannedCount;
          const conversionRate =
            rsvpYesCount > 0
              ? ((scannedCount / rsvpYesCount) * 100).toFixed(2)
              : 0;

          return {
            eventId: item.id,
            eventName: item.name,
            eventType: item.event_type,
            eventDate: item.event_date,
            location: `${item.place_name || ""}, ${item.state || ""}`.replace(
              /^,\s*|,\s*$/g,
              ""
            ),
            rsvpYesCount,
            rsvpNoCount,
            scannedCount,
            totalImpactHours: parseInt(item.totalImpactHours || 0),
            rsvpStatistics: {
              rsvpYesCount,
              rsvpNoCount,
              actualParticipants: scannedCount,
              acknowledgedButNoShow: Math.max(0, acknowledgedButNoShow),
              conversionRate: parseFloat(conversionRate),
            },
            rsvpDetails: rsvpDetails.map((detail) => ({
              rsvpId: detail.rsvp_id,
              rsvpValue: detail.rsvp_value,
              type: detail.type,
              actualHours: parseFloat(detail.actualHours || 0),
              impactHours: parseFloat(detail.impactHours || 0),
              rsvpDate: detail.rsvp_date,
              volunteerName: detail.volunteer_name,
              volunteerEmail: detail.volunteer_email,
              userId: detail.user_id,
            })),
          };
        })
      );

      return topEventsWithDetails;
    };

    // Execute all helper functions
    const [
      totalVolunteerHours,
      locationData,
      demographicData,
      rsvpStatistics,
      totalImpactHours,
      conversionFunnel,
      topVolunteers,
      topEvents,
    ] = await Promise.all([
      getTotalVolunteerHours(),
      getLocationData(),
      getDemographicData(),
      getRsvpStatistics(),
      getTotalImpactHours(),
      getConversionFunnel(),
      getTopVolunteers(),
      getTopEvents(),
    ]);

    return {
      success: true,
      data: {
        // 1. Total Volunteer Hours
        totalVolunteerHours,

        // 2. Location Data for Heatmap (Physical Events)
        locationData,

        // 3. Demographic Participation Data
        demographicData,

        // 4. RSVP Statistics
        rsvpStatistics,

        // 5. Total Impact Hours Delivered
        totalImpactHours,

        // 6. Conversion Funnel
        conversionFunnel,

        // 7. Top 10 Volunteers by Hours Contributed
        topVolunteers,

        // 8. Top 20 Events by RSVP and Participation
        topEvents,
      },
    };
  } catch (error) {
    console.error("Error in getNgoEventStatistics:", error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}

// Single Event Statistics API
async function getEventStatistics(eventId) {
  try {
    // First, verify the event exists and is an event (not a campaign)
    const event = await db.Campaign.findOne({
      where: {
        id: eventId,
        fund_raising_target: null, // Events don't have fundraising targets
      },
      include: [
        {
          model: db.Category,
          as: "categoryInfo",
          attributes: ["id", "name"],
        },
      ],
    });

    if (!event) {
      return {
        success: false,
        error: "Event not found or is not an event",
        data: null,
      };
    }

    // Helper function to get total volunteer hours for this event
    const getTotalVolunteerHours = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          SUM(cr.actualHours) as totalVolunteerHours
        FROM campaign_rsvps cr
        WHERE cr.campaign_id = :eventId
          AND cr.actualHours IS NOT NULL
      `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );
      return parseInt(result[0]?.totalVolunteerHours || 0);
    };

    // Helper function to get location data for this event (if physical)
    const getLocationData = async () => {
      if (
        event.event_type === "physical" &&
        event.latitude &&
        event.longitude
      ) {
        const result = await db.sequelize.query(
          `
          SELECT
            COUNT(cr.id) as rsvpCount,
            SUM(CASE WHEN cr.type = 'Scanned' THEN 1 ELSE 0 END) as actualParticipants
          FROM campaign_rsvps cr
          WHERE cr.campaign_id = :eventId AND cr.rsvp_value = 'yes'
        `,
          {
            replacements: { eventId },
            type: QueryTypes.SELECT,
          }
        );

        const stats = result[0];
        return {
          eventId: event.id,
          eventName: event.name,
          latitude: parseFloat(event.latitude),
          longitude: parseFloat(event.longitude),
          placeName: event.place_name,
          city: event.city,
          state: event.state,
          rsvpCount: parseInt(stats.rsvpCount || 0),
          actualParticipants: parseInt(stats.actualParticipants || 0),
        };
      }
      return null;
    };

    // Helper function to get demographic participation data for this event
    const getDemographicData = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          u.gender,
          COUNT(cr.id) as participantCount
        FROM campaign_rsvps cr
        JOIN users u ON cr.user_id = u.id
        WHERE cr.campaign_id = :eventId
          AND cr.rsvp_value = 'yes'
          AND u.gender IS NOT NULL
        GROUP BY u.gender
        ORDER BY participantCount DESC
      `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );

      const totalParticipants = result.reduce(
        (sum, item) => sum + parseInt(item.participantCount || 0),
        0
      );

      return result.map((item) => ({
        gender: item.gender,
        count: parseInt(item.participantCount || 0),
        percentage:
          totalParticipants > 0
            ? (
                (parseInt(item.participantCount || 0) / totalParticipants) *
                100
              ).toFixed(2)
            : 0,
      }));
    };

    // Helper function to get RSVP statistics for this event
    const getRsvpStatistics = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          COUNT(CASE WHEN cr.rsvp_value = 'yes' THEN 1 END) as rsvpYesCount,
          COUNT(CASE WHEN cr.rsvp_value = 'no' THEN 1 END) as rsvpNoCount,
          COUNT(CASE WHEN cr.type = 'Scanned' THEN 1 END) as actualParticipants,
          COUNT(CASE WHEN cr.rsvp_value = 'yes' AND cr.type = 'Participated' THEN 1 END) as acknowledgedButNoShow
        FROM campaign_rsvps cr
        WHERE cr.campaign_id = :eventId
      `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );

      const stats = result[0];
      const rsvpYesCount = parseInt(stats.rsvpYesCount || 0);
      const actualParticipants = parseInt(stats.actualParticipants || 0);
      const conversionRate =
        rsvpYesCount > 0
          ? ((actualParticipants / rsvpYesCount) * 100).toFixed(2)
          : 0;

      return {
        rsvpYesCount,
        rsvpNoCount: parseInt(stats.rsvpNoCount || 0),
        actualParticipants,
        acknowledgedButNoShow: parseInt(stats.acknowledgedButNoShow || 0),
        conversionRate: parseFloat(conversionRate),
      };
    };

    // Helper function to get total impact hours delivered for this event
    const getTotalImpactHours = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          SUM(cr.impactHours) as totalImpactHours
        FROM campaign_rsvps cr
        WHERE cr.campaign_id = :eventId
          AND cr.rsvp_value = 'yes'
          AND cr.type = 'Scanned'
          AND cr.impactHours IS NOT NULL
      `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );
      return parseInt(result[0]?.totalImpactHours || 0);
    };

    // Helper function to get top volunteers for this event with detailed participation data
    const getTopVolunteers = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          u.id as user_id,
          u.fullname,
          u.email,
          cr.id as rsvp_id,
          cr.actualHours as volunteerHours,
          cr.impactHours,
          cr.createdAt as participationDate,
          cr.rsvp_value,
          cr.type
        FROM campaign_rsvps cr
        JOIN users u ON cr.user_id = u.id
        WHERE cr.campaign_id = :eventId
          AND cr.type = 'Scanned'
          AND cr.actualHours IS NOT NULL
        ORDER BY cr.actualHours DESC
        LIMIT 10
      `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((item) => ({
        volunteerName: item.fullname || "Anonymous",
        email: item.email,
        userId: item.user_id,
        volunteerHours: parseFloat(item.volunteerHours || 0),
        impactHours: parseFloat(item.impactHours || 0),
        participationDate: item.participationDate,
        participationDetails: {
          rsvpId: item.rsvp_id,
          rsvpValue: item.rsvp_value,
          type: item.type,
          actualHours: parseFloat(item.volunteerHours || 0),
          impactHours: parseFloat(item.impactHours || 0),
          participationDate: item.participationDate,
        },
      }));
    };

    // Helper function to get conversion funnel data for this event with detailed RSVP data
    const getConversionFunnel = async () => {
      const result = await db.sequelize.query(
        `
        SELECT
          COUNT(cr.id) as totalInvited,
          COUNT(CASE WHEN cr.rsvp_value = 'yes' THEN 1 END) as rsvpYes,
          COUNT(CASE WHEN cr.rsvp_value = 'no' THEN 1 END) as rsvpNo,
          COUNT(CASE WHEN cr.type = 'Scanned' THEN 1 END) as participated,
          SUM(CASE WHEN cr.type = 'Scanned' AND cr.impactHours IS NOT NULL THEN cr.impactHours ELSE 0 END) as impactDelivered
        FROM campaign_rsvps cr
        WHERE cr.campaign_id = :eventId
      `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );

      // Get detailed RSVP data for this event
      const rsvpDetails = await db.sequelize.query(
        `
        SELECT
          cr.id as rsvp_id,
          cr.rsvp_value,
          cr.type,
          cr.actualHours,
          cr.impactHours,
          cr.createdAt as rsvp_date,
          u.fullname as volunteer_name,
          u.email as volunteer_email,
          u.id as user_id
        FROM campaign_rsvps cr
        JOIN users u ON cr.user_id = u.id
        WHERE cr.campaign_id = :eventId
        ORDER BY cr.createdAt DESC
        `,
        {
          replacements: { eventId },
          type: QueryTypes.SELECT,
        }
      );

      const stats = result[0];
      const totalInvited = parseInt(stats.totalInvited || 0);
      const rsvpYes = parseInt(stats.rsvpYes || 0);
      const rsvpNo = parseInt(stats.rsvpNo || 0);
      const participated = parseInt(stats.participated || 0);
      const acknowledgedButNoShow = rsvpYes - participated;

      return {
        totalInvited,
        rsvpYes,
        rsvpNo,
        participated,
        acknowledgedButNoShow: Math.max(0, acknowledgedButNoShow),
        impactDelivered: parseInt(stats.impactDelivered || 0),
        conversionRate:
          rsvpYes > 0 ? ((participated / rsvpYes) * 100).toFixed(2) : 0,
        rsvpDetails: rsvpDetails.map((detail) => ({
          rsvpId: detail.rsvp_id,
          rsvpValue: detail.rsvp_value,
          type: detail.type,
          actualHours: parseFloat(detail.actualHours || 0),
          impactHours: parseFloat(detail.impactHours || 0),
          rsvpDate: detail.rsvp_date,
          volunteerName: detail.volunteer_name,
          volunteerEmail: detail.volunteer_email,
          userId: detail.user_id,
        })),
      };
    };

    // Execute all helper functions
    const [
      totalVolunteerHours,
      locationData,
      demographicData,
      rsvpStatistics,
      totalImpactHours,
      topVolunteers,
      conversionFunnel,
    ] = await Promise.all([
      getTotalVolunteerHours(),
      getLocationData(),
      getDemographicData(),
      getRsvpStatistics(),
      getTotalImpactHours(),
      getTopVolunteers(),
      getConversionFunnel(),
    ]);

    return {
      success: true,
      data: {
        // Event basic info
        eventInfo: {
          id: event.id,
          name: event.name,
          category: event.categoryInfo?.name || "N/A",
          eventType: event.event_type,
          eventDate: event.event_date,
          eventStartTime: event.event_start_time,
          eventEndTime: event.event_end_time,
          isFullDay: event.fullday_event,
          location:
            event.event_type === "physical"
              ? `${event.place_name || ""}, ${event.city || ""}, ${
                  event.state || ""
                }`.replace(/^,\s*|,\s*$/g, "")
              : "Online",
          status: event.status,
          createdAt: event.createdAt,
        },

        // 1. Total Volunteer Hours
        totalVolunteerHours,

        // 2. Location Data (if physical event)
        locationData,

        // 3. Demographic Participation Data
        demographicData,

        // 4. RSVP Statistics
        rsvpStatistics,

        // 5. Total Impact Hours Delivered
        totalImpactHours,

        // 6. Top Volunteers for this Event
        topVolunteers,

        // 7. Conversion Funnel
        conversionFunnel,
      },
    };
  } catch (error) {
    console.error("Error in getEventStatistics:", error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
}
