const { Op } = require("sequelize");
const db = require("../_helpers/db");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

// Get all transfers with optional filters
async function getAll(params) {
  let where = {};

  if (params.ngoId) {
    where.ngo_id = params.ngoId;
  }

  if (params.transferId) {
    where.transfer_id = params.transferId;
  }

  if (params.paymentId) {
    where.payment_id = params.paymentId;
  }

  return await db.PaymentTransfer.findAll({
    where,
    order: [["id", "DESC"]],
  });
}

// Get single record by ID
async function getById(id) {
  return await getSingleRecord(id);
}

// Create a new transfer record
async function create(params) {
  const record = await db.PaymentTransfer.create(params);
  return record;
}

// Update transfer record
async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

// Delete transfer record
async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper function
async function getSingleRecord(id) {
  const record = await db.PaymentTransfer.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
