﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const ordersService = require("./order.service");
const { logAction } = require("../_helpers/logger");
const ExcelJS = require("exceljs");
const fs = require("fs");
const path = require("path");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.get("/getDonationsCalculation/byBaseAmount", getDonationsCalculation);
router.get("/getTipValues", getTipValues);
router.post("/cloneOrder", cloneOrder);
router.post("/report/excel", generateExcelReport);

router.post("/", create);
router.post("/donate", donate);
router.get("/:id", getById);
router.put("/:id", update);
router.patch("/:id", patch);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  ordersService
    .create(req.body)
    .then((response) => {
      logRequest(
        req,
        `Created a new order with order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "CREATE"
      );
      res.json({
        status: true,
        message: "Record created successfully",
        order: response,
      });
    })
    .catch(next);
}

function donate(req, res, next) {
  ordersService
    .donate(req.body)
    .then((result) => {
      logRequest(
        req,
        `Donated order with order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "CREATE"
      );
      res.json({
        status: true,
        message: "Record created successfully",
        data: result,
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  ordersService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all orders", "READ");
      res.json(records);
    })
    .catch(next);
}

function getTipValues(req, res, next) {
  const records = [20, 50, 100];
  res.json(records);
  //   ordersService
  //     .getAll(req.query)
  //     .then((records) => {
  //       logRequest(req, "Fetched all orders", "READ");
  //       res.json(records);
  //     })
  //     .catch(next);
}

function cloneOrder(req, res, next) {
  ordersService
    .cloneOrder(req.body)
    .then((response) => {
      logRequest(req, `Clone a new order with order_prefix`, "CLONED");
      res.json({
        status: true,
        message: "Record cloned successfully",
        order: response,
      });
    })
    .catch(next);
}

function getDonationsCalculation(req, res, next) {
  ordersService
    .getDonationsCalculation(req.query)
    .then((calculations) => {
      logRequest(
        req,
        `Fetched donation calculations for base amount: ${req.query.baseAmount}`,
        "READ"
      );
      res.json({
        status: true,
        message: "Donation calculations retrieved successfully",
        data: calculations,
      });
    })
    .catch(next);
}

function getById(req, res, next) {
  ordersService
    .getById(req.params.id)
    .then((user) => {
      logRequest(req, `Fetched order with ID: ${req.params.id}`, "READ");
      res.json(user);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    order_prefix: Joi.string().optional(),
    product_id: Joi.number().integer().optional().allow(null),
    bucket_id: Joi.number().integer().optional().allow(null),
    user_id: Joi.number().integer().required(),
    ngo_id: Joi.array().items(Joi.number().integer()).optional(),
    Campaigns: Joi.array().items(Joi.number().integer()).optional(),

    quantity: Joi.number().integer().min(1).optional().allow(null),
    dr_tip: Joi.number().optional().allow(null),
    price_per_unit: Joi.number().precision(2).optional().allow(null),
    total_price: Joi.number().precision(2).required(),
    order_status: Joi.string().optional().allow(null),
    payment_status: Joi.string().required(),
    shipping_address: Joi.string().optional().allow(null),
    billing_address: Joi.string().optional().allow(null),
    shipping_cost: Joi.number().precision(2).allow(null),
    discount_applied: Joi.number().precision(2).allow(null),
    remarks: Joi.string().allow(null),
    type: Joi.string().allow(null),
    donationFor: Joi.allow(null),
    campaign_id: Joi.number().integer().optional().allow(null),
    purpose: Joi.string().allow(null),
    purpose_donationday: Joi.string().allow(null),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  ordersService
    .update(req.params.id, req.body)
    .then((user) => {
      logRequest(
        req,
        `Updated order with ID: ${req.params.id}, order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "UPDATE"
      );
      res.json(user);
    })
    .catch(next);
}

function patch(req, res, next) {
  ordersService
    .patch(req.params.id, req.body)
    .then((user) => {
      logRequest(
        req,
        `Patched order with ID: ${req.params.id}, order_prefix: ${req.body.order_prefix}, quantity: ${req.body.quantity}, total_price: ${req.body.total_price}, order_status: ${req.body.order_status}, payment_status: ${req.body.payment_status}, user_id: ${req.body.user_id}`,
        "UPDATE"
      );
      res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  ordersService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted order with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

async function generateExcelReport(req, res, next) {
  try {
    const filters = req.body;
    // Get filtered data from service
    const records = await ordersService.getReportData(filters);

    // Generate Excel
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Orders Report");
    worksheet.columns = [
      { header: "Order ID", key: "order_id", width: 12 },
      { header: "User Name", key: "user_name", width: 20 },
      { header: "NGO Name", key: "ngo_name", width: 25 },
      { header: "Category Name", key: "ngo_category_names", width: 30 },

      { header: "Campaign Name", key: "campaign_name", width: 25 },

      { header: "Bucket Name", key: "bucket_name", width: 25 },

      { header: "Status", key: "status", width: 15 },
      { header: "Created At", key: "created_at", width: 20 },

      { header: "Quantity", key: "quantity", width: 10 },
      { header: "Price Per Unit", key: "price_per_unit", width: 15 },

      { header: "DR Tip", key: "dr_tip", width: 10 },
      { header: "DR Tip %", key: "dr_tip_percent", width: 10 },
      { header: "DR Tip Base", key: "dr_tip_base_amount", width: 15 },
      { header: "DR Tip GST", key: "dr_tip_total_gst", width: 15 },
      { header: "DR Tip CGST", key: "dr_tip_cgst", width: 15 },
      { header: "DR Tip SGST", key: "dr_tip_sgst", width: 15 },
      { header: "DR Tip IGST", key: "dr_tip_igst", width: 15 },
      { header: "DR Tip GST Type", key: "dr_tip_gst_type", width: 15 },
      {
        header: "Donor in Maharashtra",
        key: "is_donor_within_maharashtra",
        width: 20,
      },

      { header: "User Convenience", key: "user_convenience", width: 18 },
      { header: "User Conv. %", key: "user_convenience_percent", width: 15 },
      {
        header: "User Conv. Base",
        key: "user_convenience_base_amount",
        width: 18,
      },
      {
        header: "User Conv. GST",
        key: "user_convenience_total_gst",
        width: 18,
      },
      { header: "User Conv. CGST", key: "user_convenience_cgst", width: 18 },
      { header: "User Conv. SGST", key: "user_convenience_sgst", width: 18 },
      { header: "User Conv. IGST", key: "user_convenience_igst", width: 18 },
      {
        header: "User Conv. GST Type",
        key: "user_convenience_gst_type",
        width: 18,
      },
      {
        header: "NGO in Maharashtra",
        key: "is_ngo_within_maharashtra",
        width: 20,
      },
    ];

    records.forEach((row) => worksheet.addRow(row));

    // Save file
    const reportsDir = path.join(__basedir, "uploads", "reports");
    if (!fs.existsSync(reportsDir))
      fs.mkdirSync(reportsDir, { recursive: true });
    const fileName = `orders_report_${Date.now()}.xlsx`;
    const filePath = path.join(reportsDir, fileName);
    await workbook.xlsx.writeFile(filePath);

    // Return download link
    const downloadUrl = `/uploads/reports/${fileName}`;
    res.json({ url: downloadUrl });
  } catch (err) {
    next(err);
  }
}
