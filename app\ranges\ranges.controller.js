const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const rangesService = require("./range.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  rangesService
    .create(req.body)
    .then(() => {
      logAction(5, `Added a Range in ${req.query.pageName} `);
      return res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  rangesService
    .getAll(req.query)
    .then((records) => {
      logAction(
        5,
        `Fetched all ranges in ${req.query.pageName}`,
        req.query.pageName
      );
      return res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  rangesService
    .getById(req.params.id)
    .then((user) => res.json(user))
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.number().required(),
    moneyStartRange: Joi.number().required(),
    moneyEndRange: Joi.number().required(),
    timeStart: Joi.string().required(),
    timeEnd: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  rangesService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}

function _delete(req, res, next) {
  rangesService
    .delete(req.params.id)
    .then(() =>
      res.json({ status: true, message: "Record deleted successfully" })
    )
    .catch(next);
}
