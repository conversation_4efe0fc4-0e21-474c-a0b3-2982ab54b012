﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const authorize = require("../_middleware/authorize");
const userService = require("./user.service");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.get("/getImpactCreated/byUserId", getImpactCreated);
router.get("/notifications/config-list", getAllNotificationConfigList);

router.post("/verifyOtp", verifyOtp);
router.post("/verifyOtpForNgo", verifyOtpForNgo);
router.post("/sendVerificationOTP", sendVerificationOTP);
router.get("/sendVerificationEmail", sendVerificationEmail);
router.get("/verifyEmail", verifyEmail);
router.post("/resetPassword", resetPassword);
router.post("/", createSchema, create);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);

module.exports = router;

function create(req, res, next) {
  userService
    .create(req.body)
    .then((resObj) => {
      // if(req.body.loginType === "mobile_number") {
      //     logRequest(
      //         req,
      //         `Created a new user with Mobile: ${req?.body?.mobile_number}`,
      //         "CREATE"
      //       );
      // } else {
      //     logRequest(
      //         req,
      //         `Created a new user with Email: ${req?.body?.email}`,
      //         "CREATE"
      //       );
      // }

      res.json({
        status: true,
        message: "OTP generated successfully",
        ...resObj,
      });
    })
    .catch(next);
}

function verifyOtp(req, res, next) {
  userService
    .verifyOtp(req.body)
    .then((resObj) => res.json(resObj))
    .catch(next);
}
function verifyOtpForNgo(req, res, next) {
  userService
    .verifyOtpForNgo(req.body)
    .then((resObj) => res.json(resObj))
    .catch(next);
}
function sendVerificationOTP(req, res, next) {
  userService
    .sendVerificationOTP(req.query)
    .then((resObj) => res.json(resObj))
    .catch(next);
}

function sendVerificationEmail(req, res, next) {
  userService
    .sendVerificationEmail(req.query.email)
    .then(() =>
      res.json({
        status: true,
        message: "Email verification otp sent successfully",
      })
    )
    .catch(next);
}
function verifyEmail(req, res, next) {
  userService
    .verifyEmail(req.query)
    .then((result) => {
      if (!result.status) {
        return res.status(400).json({
          status: false,
          message: result.message,
        });
      }

      return res.json({
        status: true,
        message: "Email verified successfully",
        user: result.user,
        token: result.token,
      });
    })
    .catch(next);
}

function resetPassword(req, res, next) {
  const { id, currentPassword, newPassword, source } = req.body;

  if (!id || !newPassword || !source) {
    return res.status(400).json({
      status: false,
      message: "Missing required fields",
    });
  }

  userService
    .resetPassword({ id, currentPassword, newPassword, source })
    .then((response) => res.json(response))
    .catch((err) =>
      res.status(400).json({ status: false, message: err.message })
    );
}

function getAll(req, res, next) {
  userService
    .getAll(req.query)
    .then((records) => {
      logRequest(req, "Fetched all users", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  userService
    .getById(req.params.id)
    .then((user) => {
      logRequest(
        req,
        `Fetched user ${user?.fullname} with Mobile: ${user?.mobile_number}`,
        "READ"
      );
      res.json(user);
    })
    .catch(next);
}

function createSchema(req, res, next) {
  const schema = Joi.object({
    mobile_number: Joi.string().allow(null, ""),
    email: Joi.string().email().allow(null, ""),
    loginType: Joi.string().allow(null, ""),
  });
  validateRequest(req, next, schema);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    fullname: Joi.string().required(),
    gender: Joi.string().optional().allow(null, ""),
    skills: Joi.string().allow(null, ""),
    mobile_number: Joi.string().allow(null, ""),
    monthlyDonationGoal: Joi.number().allow(null, ""),
    place_name: Joi.string().allow(null, ""),
    latitude: Joi.string().allow(null, ""),
    longitude: Joi.string().allow(null, ""),
    pincode: Joi.string().allow(null, ""),
    state: Joi.string().allow("", null),
    age: Joi.number().allow(null, ""),
    about: Joi.string().allow(null, ""),
    skills: Joi.string().allow(null, ""),
    token: Joi.string().allow(null, ""),
    otp: Joi.string().allow(null, ""),
    primary_motivation: Joi.string().allow("", null),
    events_participation: Joi.string().allow("", null),
    status: Joi.string().allow("", null),
    dob: Joi.date().optional().allow(null),
    email: Joi.string().email().allow(null, ""),
    password: Joi.string().allow(null, ""),
    pan: Joi.string().allow(null, ""),
    occupation: Joi.string().allow(null, ""),
    citizenship: Joi.string().allow(null, ""),
    industry: Joi.string().allow(null, ""),
    interests: Joi.string().allow(null, ""),
    purposes: Joi.string().allow(null, ""),
    city: Joi.string().allow(null, ""),
    address_line_1: Joi.string().allow(null, ""),
    address_line_2: Joi.string().allow(null, ""),
    loginType: Joi.string().allow(null, ""),
    total_impact_created: Joi.number().allow(null),
    notification_settings: Joi.string().allow("", null),
  });

  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userService
    .update(req.params.id, req.body)
    .then((user) => {
      logRequest(
        req,
        `Updated user ${user?.fullname} with Mobile: ${user?.mobile_number}`,
        "UPDATE"
      );
      res.json(user);
    })
    .catch(next);
}

function _delete(req, res, next) {
  userService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted user with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function getImpactCreated(req, res, next) {
  userService
    .getImpactCreated(req.query)
    .then((records) => {
      logRequest(req, "Fetched impact created", "READ");
      res.json({
        status: true,
        message: "Impact created fetched successfully",
        data: records,
      });
    })
    .catch(next);
}

function getAllNotificationConfigList(req, res, next) {
  res.json({
    status: true,
    notificationConfigList: [
      "All Notifications",
      "New Campaigns",
      "Events around you",
      "Upcoming Activity",
      "Transactions",
    ],
  });
}
