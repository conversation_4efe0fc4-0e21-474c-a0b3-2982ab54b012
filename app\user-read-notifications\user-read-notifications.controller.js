const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const userReadNotificationService = require("./user-read-notification.service");

// routes
router.post("/", readNotificationSchema, create);
router.put("/", update);

module.exports = router;

function create(req, res, next) {
  userReadNotificationService
    .create(req.body)
    .then((record) =>
      res.json({
        status: true,
        message: "Notification marked as read",
        data: record,
      })
    )
    .catch(next);
}

function readNotificationSchema(req, res, next) {
  const schema = Joi.object({
    user_id: Joi.number().required(),
    notification_id: Joi.number().optional(),
    notifications: Joi.array().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  userReadNotificationService
    .update(req.params.id, req.body)
    .then((user) => res.json(user))
    .catch(next);
}
