﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const {
  calculateTransactionAmounts,
} = require("../_helpers/donationConstants");
const { Op, QueryTypes } = require("sequelize");


module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  getDonationSummary,
  getTransactionDetails,
  getChartData,
};

db.Transaction.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

db.Transaction.belongsTo(db.Order, {
  as: "orderInfo",
  through: "orders",
  foreignKey: "order_id",
  otherKey: "order_id",
});

// db.Transaction.belongsTo(db.Ngo, {
//   as: "ngoInfo",
//   through: "ngos",
//   foreignKey: "ngo_id",
//   otherKey: "ngo_id",
// });
// db.Transaction.belongsTo(db.Campaign, {
//   as: "campaignInfo",
//   through: "campaigns",
//   foreignKey: "campaign_id",
//   otherKey: "campaign_id",
// });

async function getAll(params, page = 1, limit = 10) {
  const { ngoId, userId, paymentId } = params;
  const where = {};
  if (ngoId) {
    where.ngo_id = ngoId;
  }
  if (paymentId) {
    where.razorpay_payment_id = paymentId;
  }
  if (userId) {
    where.user_id = userId;
  }
  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { count, rows } = await db.Transaction.findAndCountAll({
    order: [["id", "DESC"]],
    limit: parseInt(limit),
    offset: offset,

    where: where,
    include: [
      {
        model: db.User,
        as: "userInfo",
      },
      {
        model: db.Order,
        as: "orderInfo",
      },
    ],
  });

  return {
    transactions: rows,
    totalCount: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
  };
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // If transaction calculations are needed and not already provided
  // (for standalone transaction creation with base_amount, dr_tip, user_convenience, total_amount)
  if (
    params.base_amount &&
    params.dr_tip &&
    params.user_convenience &&
    params.amount &&
    !params.payment_gateway_charges
  ) {
    const transactionCalculations = calculateTransactionAmounts(
      params.amount,
      params.base_amount,
      params.dr_tip,
      params.user_convenience
    );

    // Add calculated fields to params
    Object.assign(params, {
      payment_gateway_charges: transactionCalculations.paymentGatewayCharges,
      dr_tip_after_charges: transactionCalculations.drTipAfterCharges,
      user_convenience_after_charges:
        transactionCalculations.userConvenienceAfterCharges,
      ngo_platform_convenience_fee:
        transactionCalculations.ngoPlatformConvenienceFee,
      ngo_net_donation: transactionCalculations.ngoNetDonation,
    });
  }

  const record = await db.Transaction.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to Transaction and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Transaction.findByPk(id, {
    include: [
      {
        model: db.Order,
        as: "orderInfo",
        attributes: [
          "price_per_unit",
          "total_price",
          "dr_tip",
          "payment_status",
        ],
      },
    ],
  });

  if (!record) throw new Error("Record not found");

  return record;
}

async function getDonationSummary({ donationType, userId }) {
  const where = {};
  if (userId) where.user_id = userId;
  if (donationType && donationType !== "all")
    where.donation_type = donationType;

  const transactions = await db.Transaction.findAll({ where,order: [['id', 'DESC']] });

  let totalAmount = 0;
  const donations = [];

  for (const tx of transactions) {
    if (tx.status === "captured") {
      totalAmount += parseFloat(tx.amount);
    }
    const createdAt = tx.createdAt;
    const orderId = tx.order_id;

    if (tx.donation_type === "ngo") {
      const ngo = await db.Ngo.findByPk(tx.ngo_id);
      if (ngo) {
        donations.push({
          type: "ngo",
          id: ngo.id,
          name: ngo.name,
          amount: parseFloat(tx.amount),
          createdAt,
          orderId,
          transactionId: tx.id,
          status: tx.status,
          dr_tip: Number(tx?.dr_tip_base_amount|| 0) + Number(tx?.dr_tip_total_gst || 0)
        });
      }
    } else if (tx.donation_type === "campaign") {
      const campaign = await db.Campaign.findByPk(tx.campaign_id);
      let ngoName = null;
      if (campaign) {
        const ngo = await db.Ngo.findByPk(campaign.ngo_id);
        if (ngo) ngoName = ngo.name;

        donations.push({
          type: "campaign",
          id: campaign.id,
          name: campaign.name,
          ngoName,
          amount: parseFloat(tx.amount),
          createdAt,
          orderId,
          transactionId: tx.id,
          status: tx.status,
          dr_tip: Number(tx?.dr_tip_base_amount|| 0) + Number(tx?.dr_tip_total_gst || 0)
        });
      }
    } else if (tx.donation_type === "genericViaMoney") {
      const bucket = await db.Bucket.findByPk(tx.bucket_id);
      const bucketItems = await db.BucketItem.findAll({
        where: { bucket_id: tx.bucket_id },
      });

      const uniqueNgoIds = new Set();
      let campaignCount = 0;

      for (const item of bucketItems) {
        if (item.ngo_id) uniqueNgoIds.add(item.ngo_id);
        if (item.campaign_id) campaignCount++;
      }

      if (bucket) {
        donations.push({
          type: "generic",
          name: bucket.name,
          ngoCount: uniqueNgoIds.size,
          campaignCount,
          amount: parseFloat(tx.amount),
          createdAt,
          orderId,
          transactionId: tx.id,
          status: tx.status,
          dr_tip: Number(tx?.dr_tip_base_amount|| 0) + Number(tx?.dr_tip_total_gst || 0)
        });
      }
    }
  }

  return {
    totalAmount,
    donations,
  };
}

async function getTransactionDetails(params) {
  try {
    const { transactionId } = params;

    const transaction = await db.Transaction.findByPk(transactionId);
    const orderData = await db.Order.findByPk(transaction?.order_id);
    if (!transaction) throw new Error("Transaction not found");

    const baseData = {
      id: transaction.id,
      amount: parseFloat(transaction.amount),
      processingFee: 100,
      createdAt: transaction?.createdAt,
      orderId: transaction?.order_id,
      transactionInfo: transaction,
      purpose: orderData?.purpose || "",
      purpose_donationday: orderData?.purpose_donationday || "",
      remarks: ["null", "undefined", null, undefined].includes(orderData?.remarks) ? "" : orderData?.remarks,
      pricePerUnit: orderData?.price_per_unit || "",
      totalPrice: orderData?.total_price || "",
      dr_tip: orderData?.dr_tip || "",
    };

    if (transaction.donation_type === "ngo") {
      const ngo = await db.Ngo.findByPk(transaction.ngo_id);
      if (!ngo) throw new Error("NGO not found");

      const ngoCategory = await db.NgoCategory.findOne({
        where: { ngo_id: ngo.id },
      });
      const category = ngoCategory
        ? await db.Category.findByPk(ngoCategory.category_id)
        : null;

      return {
        ...baseData,
        type: "ngo",
        ngo: {
          id: ngo.id,
          name: ngo.name,
          description: ngo.description,
          fileName: ngo.fileName,
          grade: ngo.grade,
          category: category?.name || null,
        },
      };
    }

    if (transaction.donation_type === "campaign") {
      const campaign = await db.Campaign.findByPk(transaction.campaign_id);
      if (!campaign) throw new Error("Campaign not found");
        const ngo = await db.Ngo.findByPk(campaign.ngo_id);
        const ngoCategory = await db.NgoCategory.findOne({
            where: { ngo_id: ngo.id },
        });
        const category = ngoCategory
        ? await db.Category.findByPk(ngoCategory.category_id)
        : null;
      return {
        ...baseData,
        type: "campaign",
        ngo: {
          id: ngo.id,
          name: ngo.name,
          description: ngo.description,
          fileName: ngo.fileName,
          grade: ngo.grade,
          category: category?.name || null,
        },
        campaign: {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          fileName: campaign.fileName,
        },
      };
    }

    if (transaction.donation_type === "genericViaMoney") {
      const bucket = await db.Bucket.findByPk(transaction.bucket_id);
      if (!bucket) throw new Error("Bucket not found");

      const bucketItems = await db.BucketItem.findAll({
        where: { bucket_id: bucket.id },
      });

      const ngoMap = new Map();
      const campaignMap = new Map();

      for (const item of bucketItems) {
        if (item.ngo_id && !ngoMap.has(item.ngo_id)) {
          const ngo = await db.Ngo.findByPk(item.ngo_id);
          if (ngo) {
            const ngoCategory = await db.NgoCategory.findOne({
              where: { ngo_id: ngo.id },
            });
            const category = ngoCategory
              ? await db.Category.findByPk(ngoCategory.category_id)
              : null;

            ngoMap.set(ngo.id, {
              id: ngo.id,
              name: ngo.name,
              description: ngo.description,
              fileName: ngo.fileName,
              grade: ngo.grade,
              category: category?.name || null,
            });
          }
        }

        if (item.campaign_id && !campaignMap.has(item.campaign_id)) {
          const campaign = await db.Campaign.findByPk(item.campaign_id);
          if (campaign) {
            campaignMap.set(campaign.id, {
              id: campaign.id,
              name: campaign.name,
              description: campaign.description,
              fileName: campaign.fileName,
            });
          }
        }
      }

      return {
        ...baseData,
        type: "generic",
        bucket: {
          id: bucket.id,
          name: bucket.name,
          ngos: Array.from(ngoMap.values()),
          campaigns: Array.from(campaignMap.values()),
        },
      };
    }

    throw new Error("Invalid transaction type");
  } catch (err) {
    throw err; // Let the controller or caller handle the error response
  }
}

function getStartDate(monthsAgo, now = new Date()) {
  const year = now.getFullYear();
  const month = now.getMonth() - monthsAgo + 1; // no extra +1
  return new Date(year, month, 1);
}

async function getChartData(userId, range = "3M") {
  const now = new Date();

  const ranges = {
    "3M": 3,
    "6M": 6,
    "1Y": 12,
  };

  const monthCount = ranges[range];
  if (!monthCount) throw new Error("Invalid range: use 3M, 6M, or 1Y");

  // Calculate start and end date
  const startDate = getStartDate(monthCount, now); // e.g., 2025-05-01
  const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1); // e.g., 2025-08-01
  endDate.setHours(23, 59, 59, 999); // inclusive of entire end day

  const formattedStart = startDate.toISOString().slice(0, 10); // 'YYYY-MM-DD'
  const formattedEnd = endDate.toISOString().slice(0, 10);

  // Execute RAW QUERY
  const results = await db.sequelize.query(
    `
    SELECT 
      DATE_FORMAT(createdAt, '%Y-%m') as month_key,
      SUM(impact_created) as total_impact
    FROM transactions
    WHERE 
      user_id = :userId AND 
      status = 'captured' AND 
      createdAt BETWEEN :startDate AND :endDate
    GROUP BY month_key
    ORDER BY month_key ASC
    `,
    {
      replacements: {
        userId,
        startDate: formattedStart,
        endDate: formattedEnd,
      },
      type: QueryTypes.SELECT,
    }
  );


  // Map results to object: { "2025-07": 5123, ... }
  const impactMap = Object.fromEntries(
    results.map(row => [row.month_key, Number(row.total_impact)])
  );
  // Get list of all months (YYYY-MM) in order
  const fullKeys = getMonthKeys(monthCount, now); // e.g., ["2025-05", "2025-06", "2025-07"]
  const labels = fullKeys.map(key =>
    new Date(`${key}-01`).toLocaleString("default", { month: "short" }) // → ["May", "Jun", "Jul"]
  );

const data = fullKeys.map(key => impactMap[key] || 0);

  return {
    labels,
    datasets: [{ data }],
  };
}
// Same helper as before
function getMonthKeys(n, referenceDate = new Date()) {
  const keys = [];

  // Start from N-1 months ago (includes current month)
  const start = new Date(referenceDate.getFullYear(), referenceDate.getMonth() - n + 1, 1);

  for (let i = 0; i < n; i++) {
    const d = new Date(start.getFullYear(), start.getMonth() + i, 1);
    const key = d.toISOString().slice(0, 7); // "YYYY-MM"
    keys.push(key);
  }

  return keys;
}



function getMonthLabels(n, referenceDate = new Date()) {
  const months = [];
  for (let i = n - 1; i >= 0; i--) {
    const date = new Date(
      referenceDate.getFullYear(),
      referenceDate.getMonth() - i,
      1
    );
    const month = date.toLocaleString("default", { month: "short" });
    months.push(month);
  }
  return months;
}