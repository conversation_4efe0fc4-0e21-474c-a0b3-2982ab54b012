const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    user_id: { type: DataTypes.INTEGER, allowNull: false },
    amount: { type: DataTypes.DECIMAL(10, 2), allowNull: false },
    order_id: { type: DataTypes.STRING(50), allowNull: true },
    razorpay_payment_id: { type: DataTypes.STRING(100), allowNull: true },
    razorpay_signature: { type: DataTypes.STRING(100), allowNull: true },
    status: { type: DataTypes.STRING(30), allowNull: true },
    tracking_id: { type: DataTypes.STRING(100), allowNull: true },
    payment_mode: { type: DataTypes.STRING(50), allowNull: true },
    bank_ref_no: { type: DataTypes.STRING(255), allowNull: true },
    all_values: { type: DataTypes.TEXT, allowNull: true },
    transferResponse: { type: DataTypes.TEXT, allowNull: true },
    payment_type: { type: DataTypes.STRING(200), allowNull: true },
    createdAt: { type: DataTypes.DATE, allowNull: false },
    updatedAt: { type: DataTypes.DATE, allowNull: false },
    bucket_id: { type: DataTypes.INTEGER, allowNull: null },
    ngo_id: { type: DataTypes.INTEGER, allowNull: null },
    campaign_id: { type: DataTypes.INTEGER, allowNull: null },
    donation_type: { type: DataTypes.STRING, allowNull: false },
    description: { type: DataTypes.TEXT, allowNull: true },
    impact_created: { type: DataTypes.INTEGER, allowNull: true },
    payment_gateway_charges: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    dr_tip_after_charges: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    user_convenience_after_charges: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    ngo_platform_convenience_fee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    ngo_net_donation: { type: DataTypes.DECIMAL(10, 2), allowNull: true },

    // Percentage rates applied
    dr_tip_percent: { type: DataTypes.DECIMAL(5, 4), allowNull: true },
    user_convenience_percent: {
      type: DataTypes.DECIMAL(5, 4),
      allowNull: true,
    },
    gateway_charges_percent: { type: DataTypes.DECIMAL(5, 4), allowNull: true },

    // DR Tip GST breakdown
    dr_tip_base_amount: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    dr_tip_total_gst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    dr_tip_cgst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    dr_tip_sgst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    dr_tip_igst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    dr_tip_gst_type: { type: DataTypes.STRING, allowNull: true }, // 'CGST_SGST' or 'IGST'
    is_donor_within_maharashtra: { type: DataTypes.BOOLEAN, allowNull: true },

    // User Convenience GST breakdown
    user_convenience_base_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    user_convenience_total_gst: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    user_convenience_cgst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    user_convenience_sgst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    user_convenience_igst: { type: DataTypes.DECIMAL(10, 2), allowNull: true },
    user_convenience_gst_type: { type: DataTypes.STRING, allowNull: true }, // 'CGST_SGST' or 'IGST'
    is_ngo_within_maharashtra: { type: DataTypes.BOOLEAN, allowNull: true },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("transactions", attributes, options);
}
