/**
 * Test script for subscription setup API
 * This is a simple test to verify the setupSubscription functionality
 */

const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:4000/api/payments';
const TEST_DATA = {
  // Plan Details
  planName: "Test Monthly Plan",
  amount: 50000, // ₹500 in paise
  description: "Test monthly subscription plan",
  
  // Customer Details (make sure this user exists in your database)
  name: "Test User",
  email: "<EMAIL>",
  contact: "9876543210",
  
  // Optional settings
  period: "monthly",
  interval: 1,
  currency: "INR",
  total_count: 12,
  quantity: 1,
  notes: {
    test: true,
    purpose: "testing"
  }
};

async function testSetupSubscription() {
  console.log('🚀 Testing Complete Subscription Setup...\n');
  
  try {
    console.log('📤 Sending request to:', `${BASE_URL}/setupSubscription`);
    console.log('📋 Request data:', JSON.stringify(TEST_DATA, null, 2));
    
    const response = await axios.post(`${BASE_URL}/setupSubscription`, TEST_DATA, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('\n✅ Success! Response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log('\n📊 Summary:');
      console.log(`- Plan ID: ${response.data.data.plan.id}`);
      console.log(`- Customer ID: ${response.data.data.customer.id}`);
      console.log(`- Subscription ID: ${response.data.data.subscription.id}`);
      console.log(`- Existing Customer: ${response.data.data.isExistingCustomer}`);
    }
    
  } catch (error) {
    console.log('\n❌ Error occurred:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
}

async function testIndividualAPIs() {
  console.log('\n🔧 Testing Individual APIs...\n');
  
  try {
    // Test 1: Create Plan
    console.log('1️⃣ Testing createPlan...');
    const planResponse = await axios.post(`${BASE_URL}/createPlan`, {
      name: "Individual Test Plan",
      amount: 30000,
      description: "Individual test plan creation"
    });
    console.log('✅ Plan created:', planResponse.data.plan?.id);
    
    // Test 2: Create Customer
    console.log('\n2️⃣ Testing createCustomer...');
    const customerResponse = await axios.post(`${BASE_URL}/createCustomer`, {
      name: TEST_DATA.name,
      email: TEST_DATA.email,
      contact: TEST_DATA.contact
    });
    console.log('✅ Customer ready:', customerResponse.data.customer?.id);
    
    // Test 3: Create Subscription
    if (planResponse.data.plan && customerResponse.data.customer) {
      console.log('\n3️⃣ Testing createSubscription...');
      const subscriptionResponse = await axios.post(`${BASE_URL}/createSubscription`, {
        customer_id: customerResponse.data.customer.id,
        plan_id: planResponse.data.plan.id,
        total_count: 6
      });
      console.log('✅ Subscription created:', subscriptionResponse.data.subscription?.id);
    }
    
  } catch (error) {
    console.log('❌ Individual API test failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Run tests
async function runTests() {
  console.log('🧪 Starting Subscription API Tests\n');
  console.log('=' .repeat(50));
  
  // Test the main collective API
  await testSetupSubscription();
  
  console.log('\n' + '=' .repeat(50));
  
  // Test individual APIs
  await testIndividualAPIs();
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Tests completed!');
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testSetupSubscription,
  testIndividualAPIs,
  runTests
};
