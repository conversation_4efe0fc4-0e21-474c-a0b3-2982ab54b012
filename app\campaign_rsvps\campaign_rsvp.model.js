const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    campaign_id: { type: DataTypes.INTEGER, allowNull: false },
    user_id: { type: DataTypes.INTEGER, allowNull: false },
    impactHours: { type: DataTypes.INTEGER, allowNull: true },
    actualHours: { type: DataTypes.INTEGER, allowNull: true },
    actualEventDate: { type: DataTypes.DATE, allowNull: true },
    type: { type: DataTypes.STRING, allowNull: true },
    rsvp_value: { type: DataTypes.STRING, allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("campaign_rsvps", attributes, options);
}
