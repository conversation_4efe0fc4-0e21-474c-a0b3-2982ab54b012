const { Op } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.Range.belongsTo(db.User, {
  as: "userInfo",
  through: "users",
  foreignKey: "user_id",
  otherKey: "user_id",
});

async function getAll(params) {
  let where = {};

  if (params?.userId && params?.userId !== "undefined") {
    where.user_id = params.userId;
  }

  const records = await db.Range.findAll({
    where,
    order: [["id", "DESC"]],
    include: [
      {
        model: db.User,
        as: "userInfo",
        attributes: ["id", "fullname", "skills", "age", "gender"],
      },
    ],
  });

  const now = new Date();

  const recordsWithEditableFlag = records.map((record) => {
    const updatedAt = new Date(record.updatedAt);
    const diffInMs = now - updatedAt;
    const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

    // Add editable flag
    record.setDataValue("editable", diffInDays >= 30);
    return record;
  });

  return recordsWithEditableFlag;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.Range.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Range.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
