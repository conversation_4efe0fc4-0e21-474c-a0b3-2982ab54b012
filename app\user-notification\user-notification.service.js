﻿const { Op } = require("sequelize");
const db = require("../_helpers/db");
const utils = require("../_helpers/util");
module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

db.UserNotification.belongsTo(db.Category, {
  as: "categoryInfo",
  through: "categories",
  foreignKey: "category_id",
  otherKey: "category_id",
});

async function getAll(params) {
  const { userId } = params;
  const where = {};

  let readNotifications = [];

  if (userId) {
    where.type_id = {
      [Op.or]: [userId, null],
    };
    // Exclude notifications that have been read by this user
    where.id = {
      [Op.notIn]: db.sequelize.literal(`(
        SELECT notification_id FROM user_read_notifications WHERE user_id = ${userId}
      )`)
    };
    // Get all read notification IDs for this user
    const readRows = await db.UserReadNotification.findAll({
      where: { user_id: userId },
      attributes: ["notification_id"]
    });
    const readIds = readRows.map(r => r.notification_id);
    // Fetch full notification objects for read notifications
    if (readIds.length > 0) {
      readNotifications = await db.UserNotification.findAll({
        where: { id: readIds },
        include: [
          {
            model: db.Category,
            as: "categoryInfo",
            attributes: ["id", "name", "description"],
          },
        ],
      });
    }
  }

  const notifications = await db.UserNotification.findAll({
    order: [["id", "DESC"]],
    include: [
      {
        model: db.Category,
        as: "categoryInfo",
        attributes: ["id", "name", "description"],
      },
    ],
    where,
  });
  // Return the notifications as before, but append readNotifications as a key
  return { unreadNotifications: notifications, readNotifications };
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  let deviceTokens = [];
  let matchedUserIds = [];

  if (params.category_id) {
    // Step 1: Get all users who have non-null interests
    const users = await db.User.findAll({
      where: {
        interests: {
          [Op.ne]: null,
        },
      },
    });

    const categoryId = String(params.category_id);

    // Step 2: Filter users whose interests include the category_id
    const matchedUsers = users.filter((user) => {
      const interestIds = String(user.interests)
        .split(",")
        .map((id) => id.trim());
      return interestIds.includes(categoryId);
    });

    matchedUserIds = matchedUsers.map((u) => u.id);

    // Step 3: Fetch device tokens only for matched users
    const tokens = await db.UserToken.findAll({
      where: {
        user_id: matchedUserIds,
      },
      attributes: ["device_token"],
    });

    deviceTokens = tokens.map((token) => token.device_token);
  } else {
    // Fallback: Send to all users
    const tokens = await db.UserToken.findAll({
      attributes: ["device_token"],
    });
    deviceTokens = tokens.map((token) => token.device_token);
  }

  // Step 4: Send the push notification
  await utils.sendPushNotification(deviceTokens, {
    title: params.title,
    description: params.body,
  });

  // Step 5: Save notification in DB
  const record = await db.UserNotification.create(params);

  if (matchedUserIds.length > 0) {
    const readRecords = matchedUserIds.map((userId) => ({
      user_id: userId,
      notification_id: record.id,
    }));

    await db.UserReadNotification.bulkCreate(readRecords);
  }
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper
async function getSingleRecord(id) {
  const record = await db.UserNotification.findByPk(id);
  if (!record) throw "Notification not found";
  return record;
}
