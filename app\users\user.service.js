﻿const db = require("../_helpers/db");
const utils = require("../_helpers/util");
const jwt = require("jsonwebtoken");
const config = require("../../config.json");
const { default: axios } = require("axios");
const bcrypt = require("bcryptjs");
const { Op, QueryTypes } = require("sequelize");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
  generateOtp,
  verifyOtp,
  sendVerificationOTP,
  verifyOtpForNgo,
  sendVerificationEmail,
  verifyEmail,
  resetPassword,
  getImpactCreated,
};

async function getAll() {
  return await db.User.findAll();
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const loginType = params?.loginType || "mobile";
  const where = {};
  if (loginType === "mobile") {
    where.mobile_number = params?.mobile_number;
  } else {
    where.email = params?.email;
  }
  const checkIfRecordExists = await db.User.findOne({
    where: where,
  });
  const otp = utils.generateOtp();
  if (!checkIfRecordExists?.id) {
    // send otp
    // params.otp = otp;
    if (loginType === "mobile" || loginType === "email") {
      params.otp = otp;
    }
    if (loginType === "email" && params.password) {
      params.password = await bcrypt.hash(params.password, 10);
    }
    const userRecord = await db.User.create(params);
    if (loginType === "mobile" || loginType === "email") {
      await generateOtp(params, loginType);
    }
    return { isNewRegistration: true, ...userRecord?.dataValues };
  } else {
    Object.assign(checkIfRecordExists, { otp: otp });
    await checkIfRecordExists.save();
    if (loginType === "mobile") {
      await generateOtp(checkIfRecordExists, loginType);
      return true;
    } else {
      const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
        expiresIn: "7d",
      });
      return { ...checkIfRecordExists?.dataValues, token };
    }
  }
}

async function sendVerificationOTP(params) {
  const checkIfRecordExists = await db.Ngo.findOne({
    where: { point_of_contact_mobile_number: params?.mobile_number },
  });

  if (!checkIfRecordExists) {
    return {
      status: false,
      message: "No NGO found for this mobile number",
    };
  }

  const otp = utils.generateOtp();
  params.otp = otp;

  const response = await generateOtp(params);

  if (response) {
    return {
      status: true,
      message: "OTP has been sent to your mobile number",
    };
  } else {
    return {
      status: false,
      message: "Something went wrong, please try again later",
    };
  }
}

async function verifyOtp(params) {
  const loginType = params?.loginType || "mobile";
  const where = {};
  if (loginType === "mobile") {
    where.mobile_number = params?.mobile_number;
    where.otp = params.otp;
  } else {
    where.email = params?.email;
    where.otp = params.otp;
  }
  const checkIfRecordExists = await db.User.findOne({
    where: where,
  });
  if (!checkIfRecordExists?.id) {
    return { status: true, message: "No user found", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists, token };
  }
}

async function verifyOtpForNgo(params) {
  const checkIfRecordExists = await db.Ngo.findOne({
    where: { point_of_contact_mobile_number: params?.mobile_number },
  });
  if (!checkIfRecordExists?.id) {
    return { status: false, message: "No Ngo found", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists };
  }
}

async function sendVerificationEmail(email) {
  const params = { email: email };
  const user = await db.User.findOne({ where: { email } });
  if (!user) {
    throw "User not found";
  }

  const otp = utils.generateOtp();
  params.emailotp = otp;

  const [updatedCount] = await db.User.update(
    { emailotp: otp },
    {
      where: { email: params.email },
    }
  );

  if (updatedCount === 0) {
    return {
      status: false,
      message: "No user found for this email or OTP not saved",
    };
  }

  const emailTemplate = await db.CommunicationEmail.findOne({
    where: { id: 9 },
  });

  if (!emailTemplate) {
    throw "Email template not found.";
  }

  const emailBody = utils.replacePlaceholders(emailTemplate.template, {
    fullname: user?.fullname,
    otp: otp,
  });

  const emailStatus = await utils.sendEmail(
    user.email,
    emailTemplate.subject,
    emailBody
  );
  if (!emailStatus) {
    throw "Failed to send verification email. Please try again.";
  }

  return true;
}

async function verifyEmail(params) {
  const checkIfRecordExists = await db.User.findOne({
    where: { email: params?.email, emailotp: params?.emailotp },
  });
  if (!checkIfRecordExists?.id) {
    return { status: false, message: "Verification failed", user: null };
  } else {
    const token = jwt.sign({ sub: checkIfRecordExists.id }, config.secret, {
      expiresIn: "7d",
    });
    return { status: true, user: checkIfRecordExists, token };
  }
}

async function resetPassword({ id, currentPassword, newPassword, source }) {
  const user = await db.User.findByPk(id);
  if (!user) {
    throw new Error("User not found");
  }

  if (source === "userprofile") {
    if (!currentPassword) {
      throw new Error("Current password is required");
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      throw new Error("Current password is incorrect");
    }
  }

  const hashedPassword = await bcrypt.hash(newPassword, 10);
  user.password = hashedPassword;
  await user.save();

  return {
    status: true,
    message:
      source === "userprofile"
        ? "Password updated successfully"
        : "Password reset successfully",
  };
}

async function update(id, params) {
  // copy params to user and save
  const record = await getSingleRecord(id);
  params.status = "Active";
  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  record.status = "Inactive";
  await record.save();
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.User.findByPk(id);
  if (!record) throw "Record not found";

  // Fields to check (excluding email and mobile_number for special logic)
  const totalFields = [
    "fullname",
    "gender",
    // "mobile_number", <-- exclude from base list
    "monthlyDonationGoal",
    "pincode",
    "state",
    "about",
    "skills",
    "otp",
    "dob",
    // "email", <-- exclude from base list
    "pan",
    "occupation",
    "interests",
    "primary_motivation",
    "city",
    "address_line_1",
    "address_line_2",
  ];

  let filledFields = totalFields.filter(
    (field) => record[field] && record[field] !== ""
  ).length;

  // Handle mutually inclusive email/mobile_number logic
  const hasPhone = record["mobile_number"] && record["mobile_number"] !== "";
  const hasEmail = record["email"] && record["email"] !== "";

  // If at least one is present, count 1 extra field as filled
  if (hasPhone || hasEmail) {
    filledFields += 1;
  }

  // Add 1 to totalFields length to account for (email or phone)
  const totalFieldCount = totalFields.length + 1;

  const completionPercentage = Math.round(
    (filledFields / totalFieldCount) * 100
  );

  record.setDataValue("completionPercentage", completionPercentage);
  return record;
}

async function generateOtp(params, loginType = "mobile") {
  if (loginType === "mobile") {
    const message = encodeURIComponent(
      `${params.otp} is the OTP to verify your mobile number with DoRight. Please do not share this OTP with anyone.-DORGHT`
    );
    await axios.get(
      `https://sms6.rmlconnect.net:8443/bulksms/bulksms?username=zensocio&password=y8lG-%5BD7&type=0&dlr=1&destination=91${params?.mobile_number}&source=DORGHT&message=${message}&entityid=1201173131450491942&tempid=1207173349829291055`
    );
    return true;
  } else {
    // const emailTemplate = await db.CommunicationEmail.findOne({
    //     where: {
    //         id: 2,
    //     },
    // });
    // if (emailTemplate) {
    const name = `${params.fullname}`;
    ///body for ngo
    const emailBody = `<h2 style="color: #333;">Login Verification</h2>
        <p style="font-size: 16px; color: #555;">Hi ${name},</p>
        <p style="font-size: 16px; color: #555;">
          Use the one-time password (OTP) below to log in to your account.</strong>.
        </p>
        <div style="text-align: center; margin: 30px 0;">
          <span style="font-size: 32px; letter-spacing: 8px; font-weight: bold; color: #000;">${params?.otp}</span>
        </div>
        <p style="font-size: 14px; color: #999;">
          If you did not request this login, you can safely ignore this email.
        </p>
        <p style="font-size: 16px; color: #555;">Thanks,<br/>The DoRight Team</p>`;

    const emailStatus = await utils.sendEmail(
      `${params.email}`,
      `Your One-Time Password (OTP) for Login`,
      emailBody
    );

    if (!emailStatus) {
      return false;
    }
    return true;
  }
  // }
}

async function getImpactCreated({ userId }) {
  const user = await db.User.findByPk(userId);
  if (!user) throw new Error("User not found");

  // --- Step 1: Get User Range Info ---
  let monthlyDonationGoal = 2000;
  let projectedHoursSpent = 24 / 12;
  const userRange = await db.Range.findOne({ where: { user_id: userId } });

  if (userRange) {
    if (userRange.moneyEndRange) {
      monthlyDonationGoal = userRange.moneyEndRange / 12;
    }
    if (userRange.timeEnd) {
      const cleaned = userRange.timeEnd.replace(/[^\d.]/g, "");
      const parsed = Number(cleaned);
      projectedHoursSpent = isNaN(parsed) ? 24 / 12 : parsed / 12;
    }
  }

    const totalImpactCreated = user.total_impact_created || 0;

    // --- Step 2: Fetch Transactions ---
    const transactions = await db.Transaction.findAll({
        where: { user_id: userId, status: "captured" },
    });

  let totalContribution = 0;
  const categoryContribution = {};
  const campaignIdSet = new Set();
  const bucketIdSet = new Set();
  const orderIdSet = new Set();

  for (const txn of transactions) {
    const amount = Number(txn.amount || 0);
    totalContribution += amount;

    if (txn.donation_type === "campaign" && txn.campaign_id) {
      campaignIdSet.add(txn.campaign_id);
    }

    if (txn.bucket_id) {
      bucketIdSet.add(txn.bucket_id);
    }

    if (txn.order_id) {
      orderIdSet.add(txn.order_id);
    }
  }

  // --- Step 3: Fetch Campaigns via order_campaigns using orderIds ---
  if (orderIdSet.size > 0) {
    const orderCampaigns = await db.OrderCampaign.findAll({
      where: { order_id: Array.from(orderIdSet) },
      attributes: ["campaign_id"],
    });

    for (const oc of orderCampaigns) {
      if (oc.campaign_id) {
        campaignIdSet.add(oc.campaign_id);
      }
    }
  }

  const campaignCount = campaignIdSet.size;

  // --- Step 4: Aggregate Category Contributions from Campaigns ---
  const campaigns = await db.Campaign.findAll({
    where: { id: Array.from(campaignIdSet) },
    attributes: ["id", "category_id"],
  });

  const campaignCategoryMap = new Map();
  for (const campaign of campaigns) {
    if (campaign.category_id) {
      campaignCategoryMap.set(campaign.id, campaign.category_id);
    }
  }

  for (const txn of transactions) {
    const amount = Number(txn.amount || 0);
    let campaignId = txn.campaign_id;

    if (!campaignId && txn.order_id) {
      const matchingCampaign = await db.OrderCampaign.findOne({
        where: { order_id: txn.order_id },
      });
      campaignId = matchingCampaign?.campaign_id;
    }

    const categoryId = campaignCategoryMap.get(campaignId);
    if (categoryId) {
      categoryContribution[categoryId] =
        (categoryContribution[categoryId] || 0) + amount;
    }
  }

  // --- Step 5: Causes Impacted ---
  const causesImpacted = [];
  const totalForPercentage = Object.values(categoryContribution).reduce(
    (a, b) => a + b,
    0
  );

  const categoryIds = Object.keys(categoryContribution);
  if (categoryIds.length > 0) {
    const categories = await db.Category.findAll({
      where: { id: categoryIds },
    });
    const categoryMap = new Map(categories.map((cat) => [cat.id, cat.name]));

    for (const [categoryId, amount] of Object.entries(categoryContribution)) {
      const percentage = ((amount / totalForPercentage) * 100).toFixed(2);
      causesImpacted.push({
        category:
          categoryMap.get(Number(categoryId)) || `Category ${categoryId}`,
        percentage: Number(percentage),
      });
    }
  }

  // --- Step 6: Lives Impacted This Month ---
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  const impactTxns = await db.Transaction.findAll({
    where: {
      user_id: userId,
      status: "captured",
      razorpay_payment_id: { [Op.ne]: null },
      createdAt: { [Op.between]: [firstDay, lastDay] },
    },
  });

  let livesImpacted = impactTxns.reduce(
    (sum, txn) => sum + (Number(txn.impact_created) || 0),
    0
  );

  // --- Step 7: Projected Impact from Interests ---
  let projectedImpact = 0;
  if (user.interests) {
    const interestIds = user.interests
      .split(",")
      .map((id) => id.trim())
      .filter((id) => !!id && !isNaN(id));

    if (interestIds.length > 0) {
      const interestCategories = await db.Category.findAll({
        where: { id: interestIds },
      });

      const avgRatio =
        interestCategories.reduce(
          (sum, cat) => sum + Number(cat.ratio || 1),
          0
        ) / interestCategories.length || 1;

      projectedImpact = Number((monthlyDonationGoal / avgRatio).toFixed(2));
    }
  }

  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(
    now.getFullYear(),
    now.getMonth() + 1,
    0,
    23,
    59,
    59
  );

  const monthlyRsvps = await db.CampaignRsvps.findAll({
    where: {
      user_id: userId,
      actualEventDate: {
        [Op.between]: [startOfMonth, endOfMonth],
      },
      actualHours: { [Op.ne]: null },
      impactHours: { [Op.ne]: null },
      type:"scanned"
    },
    attributes: ["actualHours", "impactHours"],
  });

  let totalActualHours = 0;
  let totalImpactHours = 0;

  monthlyRsvps.forEach((rsvp) => {
    totalActualHours += Number(rsvp.actualHours) || 0;
    totalImpactHours += Number(rsvp.impactHours) || 0;
  });

  livesImpacted += totalImpactHours;

  // --- Step 8: Impact Ring ---
  let impactRing = "RingQuarter";
  if (livesImpacted > 0 && projectedImpact > 0) {
    const step = projectedImpact / 5;
    if (livesImpacted <= step) impactRing = "RingQuarter";
    else if (livesImpacted <= step * 2) impactRing = "RingHalf";
    else if (livesImpacted <= step * 3) impactRing = "RingThreeQuarter";
    else if (livesImpacted <= step * 4) impactRing = "Ring90";
    else impactRing = "RingFull";
  }

  // --- Step 9: Impact Message ---
  const impactMessage =
    parseInt(livesImpacted) >= parseInt(projectedImpact)
      ? "You have achieved your goal for this month!"
      : `${
          parseInt(projectedImpact) - parseInt(livesImpacted)
        } lives away from your monthly target impact`;

  // --- Step 10: Top Donated Categories ---
  let topDonatedCategories = null;
  if (parseInt(projectedImpact) > 0) {
    const year = now.getFullYear();
    const amountLeft =
      parseInt(userRange?.moneyEndRange || 0) - parseInt(totalContribution);
    topDonatedCategories = await getTopDonatedCategories(
      userId,
      year,
      amountLeft
    );
  }

  // --- Final Result ---
  return {
    monthlyDonationGoal: parseInt(monthlyDonationGoal),
    impactScore: parseInt(totalImpactCreated),
    livesImpacted: parseInt(livesImpacted),
    projectedImpact: projectedImpact === 0 ? 100 : parseInt(projectedImpact),
    impactMessage: projectedImpact === 0 ? null : impactMessage,
    totalContribution: parseInt(totalContribution),
    campaignCount,
    hoursSpent: parseInt(totalActualHours),
    projectedHoursSpent: parseInt(projectedHoursSpent),
    causesImpacted,
    topDonatedCategories,
    impactRing,
  };
}

async function getTopDonatedCategories(
  userId,
  year = new Date().getFullYear(),
  amountToBeDonated
) {
  const COLORS = ["#FFA500", "#FFBA78", "#FFC999", "#FFC955"];
  const results = await db.sequelize.query(
    `
  SELECT
    user_id,
    category_id,
    category_name,
    SUM(total_donated) AS total_donated
  FROM (
    -- Campaign Donations
    SELECT
      t.user_id,
      oc.cause_id AS category_id,
      cat.name AS category_name,
      SUM(t.amount) AS total_donated
    FROM transactions t
    JOIN order_campaigns oc ON oc.order_id = t.order_id
    JOIN categories cat ON cat.id = oc.cause_id
    WHERE t.user_id = :userId
      AND t.status = 'captured'
      AND YEAR(t.createdAt) = :year
    GROUP BY t.user_id, oc.cause_id, cat.name

    UNION ALL

    -- NGO Donations
    SELECT
      t.user_id,
      ongs.cause_id AS category_id,
      cat.name AS category_name,
      SUM(t.amount) AS total_donated
    FROM transactions t
    JOIN order_ngos ongs ON ongs.order_id = t.order_id
    JOIN categories cat ON cat.id = ongs.cause_id
    WHERE t.user_id = :userId
      AND t.status = 'captured'
      AND YEAR(t.createdAt) = :year
    GROUP BY t.user_id, ongs.cause_id, cat.name

    UNION ALL

    -- Bucket Donations
    SELECT
      t.user_id,
      bi.category_id AS category_id,
      cat.name AS category_name,
      SUM(t.amount) AS total_donated
    FROM transactions t
    JOIN bucket_items bi ON bi.bucket_id = t.bucket_id
    JOIN categories cat ON cat.id = bi.category_id
    WHERE t.user_id = :userId
      AND t.status = 'captured'
      AND t.bucket_id IS NOT NULL
      AND YEAR(t.createdAt) = :year
    GROUP BY t.user_id, bi.category_id, cat.name
  ) AS combined
  GROUP BY user_id, category_id, category_name
  ORDER BY total_donated DESC;
`,
    {
      replacements: { userId, year },
      type: QueryTypes.SELECT,
    }
  );

  const formatted = results
    .map((r) => ({
      ...r,
      total_donated: parseFloat(r.total_donated),
    }))
    .sort((a, b) => b.total_donated - a.total_donated);

  if (formatted.length === 0) {
    return [{ label: "", color: "#444", size: 100 }];
  }

  const topThree = formatted.slice(0, 3);
  const others = formatted.slice(3);

  const topThreeTotal = topThree.reduce((sum, r) => sum + r.total_donated, 0);
  const othersTotal = others.reduce((sum, r) => sum + r.total_donated, 0);
  const totalContributed = topThreeTotal + othersTotal;

  // Avoid divide-by-zero
  if (totalContributed === 0 || amountToBeDonated === 0) {
    return [{ label: "", color: "#444", size: 100 }];
  }

  const chartData = [];

  // Calculate proportion from combined top+others
  topThree.forEach((item, index) => {
    const size = (item.total_donated / totalContributed) * 100;
    chartData.push({
      label: item.category_name,
      color: COLORS[index],
      size: parseFloat(size.toFixed(2)),
    });
  });

  if (others.length > 0) {
    const othersSize = (othersTotal / totalContributed) * 100;
    chartData.push({
      label: "Others",
      color: COLORS[3],
      size: parseFloat(othersSize.toFixed(2)),
    });
  }

  // Ensure total = 100
  const usedSize = chartData.reduce((sum, entry) => sum + entry.size, 0);
  const remainder = Math.max(0, 100 - usedSize);

  chartData.push({
    label: "",
    color: "#444",
    size: parseFloat(remainder.toFixed(2)),
  });

  return chartData;
}
