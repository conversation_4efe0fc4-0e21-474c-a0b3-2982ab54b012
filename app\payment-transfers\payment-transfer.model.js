const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    payment_id: { type: DataTypes.STRING, allowNull: false }, // Razorpay payment ID
    order_id: { type: DataTypes.INTEGER, allowNull: true }, // Razorpay order ID
    transaction_id: { type: DataTypes.INTEGER, allowNull: true }, // Your internal donation/payment reference
    ngo_account_id: { type: DataTypes.STRING, allowNull: false }, // Razorpay NGO account ID
    ngo_id: { type: DataTypes.INTEGER, allowNull: false }, // Your internal NGO reference
    amount: { type: DataTypes.DECIMAL(10, 2), allowNull: false }, // Amount transferred to NGO
    currency: { type: DataTypes.STRING(5), allowNull: false, defaultValue: "INR" },
    transfer_id: { type: DataTypes.STRING, allowNull: true }, // Razorpay transfer ID
    status: { 
      type: DataTypes.ENUM("INITIATED", "SUCCESS", "FAILED"), 
      allowNull: false, 
    },
    notes: { type: DataTypes.STRING, allowNull: true }, // Store Razorpay notes
    response_data: { type: DataTypes.STRING, allowNull: true }, // Store full Razorpay API response
  };

  const options = {
    tableName: "payment_transfers",
    defaultScope: {
      attributes: { exclude: [] }, // you can exclude sensitive fields if any
    },
    scopes: {
      withResponse: { attributes: {} }, // scope if you want raw response data too
    },
  };

  return sequelize.define("payment_transfers", attributes, options);
}
