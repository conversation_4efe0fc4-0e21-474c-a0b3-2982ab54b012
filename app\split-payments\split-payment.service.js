const config = require("../../config.json");
const Razorpay = require("razorpay");
const db = require("../_helpers/db");

const KEY_ID = config.RAZORPAY_KEY_ID;
const KEY_SECRET = config.RAZORPAY_KEY_SECRET;
const razorpay = new Razorpay({ key_id: KEY_ID, key_secret: KEY_SECRET });

module.exports = {
  createPlan,
  createCustomer,
  createSubscription,
  setupSubscription,
  pauseSubscription,
  resumeSubscription,
  cancelSubscription,
  getSubscriptionStatus,
};

/**
 * Create a Razorpay plan
 */
async function createPlan(planData) {
  const {
    period = "monthly",
    interval = 1,
    name,
    amount,
    currency = "INR",
    description,
  } = planData;

  if (!name || !amount || !description) {
    throw new Error("Missing required fields: name, amount, or description");
  }

  try {
    const plan = await razorpay.plans.create({
      period,
      interval,
      item: {
        name,
        amount, // Should be in paise (e.g., ₹500 = 50000)
        currency,
        description,
      },
    });

    return { success: true, plan };
  } catch (err) {
    console.error("Plan creation failed:", err);
    throw new Error(`Failed to create plan: ${err.message}`);
  }
}

/**
 * Create or get existing Razorpay customer
 */
async function createCustomer(customerData) {
  const { name, email, contact } = customerData;

  try {
    const user = await db.User.findOne({
      where: { email, mobile_number: contact },
    });

    if (!user) {
      throw new Error("User not found with provided email and contact");
    }

    // If customerResponse already stored, return existing customer
    if (user.customerResponse) {
      return {
        success: true,
        message: "Customer already exists",
        customer: JSON.parse(user.customerResponse),
        user,
        isExisting: true,
      };
    }

    const customer = await razorpay.customers.create({ name, email, contact });

    // Update user with Razorpay response
    await user.update({
      customerResponse: JSON.stringify(customer),
    });

    return {
      success: true,
      message: "Customer created successfully",
      customer,
      user,
      isExisting: false,
    };
  } catch (err) {
    console.error("Customer creation error:", err);
    throw new Error(`Failed to create customer: ${err.message}`);
  }
}

/**
 * Create a Razorpay subscription
 */
async function createSubscription(subscriptionData) {
  const {
    customer_id,
    plan_id,
    total_count = 12,
    quantity = 1,
    notes = {},
  } = subscriptionData;

  if (!customer_id || !plan_id) {
    throw new Error("customer_id and plan_id are required");
  }

  try {
    const subscription = await razorpay.subscriptions.create({
      plan_id,
      customer_id,
      customer_notify: 1,
      total_count, // e.g., 12 months
      quantity, // Optional
      notes, // Optional
    });

    return { success: true, subscription };
  } catch (err) {
    console.error("Subscription creation failed:", err);
    throw new Error(`Failed to create subscription: ${err.message}`);
  }
}

/**
 * Complete subscription setup - creates plan, customer (if needed), and subscription
 */
async function setupSubscription(setupData) {
  const {
    // Plan data
    period = "monthly",
    interval = 1,
    planName,
    amount,
    currency = "INR",
    description,
    // Customer data
    name,
    email,
    contact,
    // Subscription data
    total_count = 12,
    quantity = 1,
    notes = {},
  } = setupData;

  try {
    // Step 1: Create Plan
    console.log("Creating plan...");
    const planResult = await createPlan({
      period,
      interval,
      name: planName,
      amount,
      currency,
      description,
    });

    const plan = planResult.plan;
    console.log("Plan created:", plan.id);

    // Step 2: Create or get Customer
    console.log("Creating/getting customer...");
    const customerResult = await createCustomer({
      name,
      email,
      contact,
    });

    const customer = customerResult.customer;
    console.log("Customer ready:", customer.id);

    // Step 3: Create Subscription
    console.log("Creating subscription...");
    const subscriptionResult = await createSubscription({
      customer_id: customer.id,
      plan_id: plan.id,
      total_count,
      quantity,
      notes,
    });

    const subscription = subscriptionResult.subscription;
    console.log("Subscription created:", subscription.id);

    return {
      success: true,
      message: "Subscription setup completed successfully",
      data: {
        plan,
        customer,
        subscription,
        isExistingCustomer: customerResult.isExisting,
      },
    };
  } catch (err) {
    console.error("Subscription setup failed:", err);
    throw new Error(`Subscription setup failed: ${err.message}`);
  }
}

/**
 * Pause a subscription
 */
async function pauseSubscription(subscriptionId, pause_at = "now") {
  try {
    await razorpay.subscriptions.pause(subscriptionId, { pause_at });
    return { success: true, message: "Subscription paused" };
  } catch (err) {
    console.error("Pause subscription failed:", err);
    throw new Error(`Failed to pause subscription: ${err.message}`);
  }
}

/**
 * Resume a subscription
 */
async function resumeSubscription(subscriptionId, resume_at = "now") {
  try {
    await razorpay.subscriptions.resume(subscriptionId, { resume_at });
    return { success: true, message: "Subscription resumed" };
  } catch (err) {
    console.error("Resume subscription failed:", err);
    throw new Error(`Failed to resume subscription: ${err.message}`);
  }
}

/**
 * Cancel a subscription
 */
async function cancelSubscription(subscriptionId, cancel_at_cycle_end = false) {
  try {
    const response = await razorpay.subscriptions.cancel(
      subscriptionId,
      cancel_at_cycle_end
    );
    return { success: true, response };
  } catch (err) {
    console.error("Cancel subscription failed:", err);
    throw new Error(`Failed to cancel subscription: ${err.message}`);
  }
}

/**
 * Get Subscription Status from Razorpay
 */
// async function getSubscriptionStatus(subscriptionId) {
//   if (!subscriptionId) {
//     throw new Error("Subscription ID is required");
//   }

//   try {
//     // Fetch subscription details
//     const subscription = await razorpay.subscriptions.fetch(subscriptionId);

//     // Transform response for frontend usage
//     return {
//       success: true,
//       subscription: subscription,
//     };
//   } catch (err) {
//     console.error("Get subscription status failed:", err);
//     throw new Error(`Failed to fetch subscription status: ${err.message}`);
//   }
// }
async function getSubscriptionStatus(subscriptionId) {
  if (!subscriptionId) {
    throw new Error("Subscription ID is required");
  }

  try {
    // Fetch subscription details
    const subscription = await razorpay.subscriptions.fetch(subscriptionId);

    // Transform response for frontend usage
    return {
      success: true,
      subscription: {
        id: subscription.id,
        status: subscription.status,
        planId: subscription.plan_id,
        customerId: subscription.customer_id,
        startDate: new Date(subscription.start_at * 1000).toLocaleString(),
        endDate: new Date(subscription.end_at * 1000).toLocaleString(),
        currentStart: new Date(
          subscription.current_start * 1000
        ).toLocaleString(),
        currentEnd: new Date(subscription.current_end * 1000).toLocaleString(),
        nextChargeDate: subscription.charge_at
          ? new Date(subscription.charge_at * 1000).toLocaleString()
          : null,
        paidCount: subscription.paid_count,
        totalCount: subscription.total_count,
        remainingCount: subscription.remaining_count,
        notes: subscription.notes,
        shortUrl: subscription.short_url,
        paymentMethod: subscription.payment_method,
        createdAt: new Date(subscription.created_at * 1000).toLocaleString(),
      },
    };
  } catch (err) {
    console.error("Get subscription status failed:", err);
    throw new Error(`Failed to fetch subscription status: ${err.message}`);
  }
}
