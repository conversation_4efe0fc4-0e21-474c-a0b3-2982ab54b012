const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    type: { type: DataTypes.STRING, allowNull: false },
    typeid: { type: DataTypes.INTEGER, allowNull: false },
    dateRecorded: { type: DataTypes.DATEONLY, allowNull: false },
  };

  const options = {
    defaultScope: {
      // exclude hash by default
    },
    scopes: {
      // include hash with this scope
      withHash: { attributes: {} },
    },
  };

  return sequelize.define("dashboard_stats", attributes, options);
}
