const db = require("../_helpers/db");
const utils = require("../_helpers/util");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params, isadmin) {
  const whereClause = isadmin === "yes" ? {} : { status: "Active" };

  // Return only id and name if isFilterOption is true
  if (params?.isFilterOption === "yes") {
    const categories = await db.Category.findAll({
      where: { status: "Active" },
      attributes: ["id", "name"],
      order: [["name", "DESC"]],
    });
    return categories;
  }

  const categories = await db.Category.findAll({
    where: whereClause,
    order: [["id", "DESC"]],
  });

  for (let category of categories) {
    const images = await db.CategoryImage.findAll({
      where: { category_id: category?.id },
    });
    category.dataValues.images = images;
  }

  return categories;
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  // validate
  params.slug = utils.generateSlug(params.name);
  if (await db.Category.findOne({ where: { slug: params.slug } })) {
    throw 'Record "' + params.name + '" is already taken';
    return;
  }

  const record = await db.Category.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);

  params.slug = utils.generateSlug(params.name);
  // validate

  const recordChanged = params.slug && record.slug !== params.slug;
  if (
    recordChanged &&
    (await db.Category.findOne({ where: { slug: params.slug } }))
  ) {
    throw 'Category "' + params.name + '" is already taken';
  }

  // copy params to user and save
  Object.assign(record, params);
  await record.save();

  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
  return record;
}

// helper functions

async function getSingleRecord(id) {
  const record = await db.Category.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
