const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    user_id: { type: DataTypes.INTEGER, allowNull: false }, // FK to users table
    razorpay_subscription_id: { type: DataTypes.STRING, allowNull: false },
    plan_id: { type: DataTypes.STRING, allowNull: false },
    status: { type: DataTypes.STRING, allowNull: true, defaultValue: "active" }, // sync from Razorpay
  };

  const options = {};

  return sequelize.define("user_subscriptions", attributes, options);
}
