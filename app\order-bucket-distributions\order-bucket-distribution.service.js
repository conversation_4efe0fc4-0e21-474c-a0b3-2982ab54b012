const db = require("../_helpers/db");

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: _delete,
};

async function getAll(params) {
  let where = {};
  if (params && params.order_id) {
    where.order_id = params.order_id;
  }
  if (params && params.bucket_id) {
    where.bucket_id = params.bucket_id;
  }
  if (params && params.ngo_id) {
    where.ngo_id = params.bucket_id;
  }
  if (params && params.transaction_id) {
    where.transaction_id = params.transaction_id;
  }
  return await db.OrderBucketDistribution.findAll({
    where,
    order: [["id", "DESC"]],
  });
}

async function getById(id) {
  return await getSingleRecord(id);
}

async function create(params) {
  const record = await db.OrderBucketDistribution.create(params);
  return record;
}

async function update(id, params) {
  const record = await getSingleRecord(id);
  Object.assign(record, params);
  await record.save();
  return record.get();
}

async function _delete(id) {
  const record = await getSingleRecord(id);
  await record.destroy();
}

// helper functions
async function getSingleRecord(id) {
  const record = await db.OrderBucketDistribution.findByPk(id);
  if (!record) throw "Record not found";
  return record;
}
