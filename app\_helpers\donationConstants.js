const DR_TIP_PERCENT = 0.08; // 8%
// const USER_CONVENIENCE_PERCENT = 0.05; // 5%
const USER_CONVENIENCE_PERCENT = 0; // 5%
const GATEWAY_CHARGES_PERCENT = 0.0118; // 1.18%
const NGO_PLATFORM_PERCENT = 0.08; // 8%

// GST Constants
const CGST_PERCENT = 0.09; // 9%
const SGST_PERCENT = 0.09; // 9%
const IGST_PERCENT = 0.18; // 18%
const TOTAL_GST_PERCENT = 0.18; // 18% (CGST + SGST or IGST)

function calculateGSTBreakdown(amountInclGST, isWithinMaharashtra) {
  const baseAmount = amountInclGST / (1 + TOTAL_GST_PERCENT);
  const totalGST = amountInclGST - baseAmount;

  if (isWithinMaharashtra) {
    // CGST + SGST
    const cgst = baseAmount * CGST_PERCENT;
    const sgst = baseAmount * SGST_PERCENT;
    return {
      baseAmount: parseFloat(baseAmount.toFixed(2)),
      totalGST: parseFloat(totalGST.toFixed(2)),
      cgst: parseFloat(cgst.toFixed(2)),
      sgst: parseFloat(sgst.toFixed(2)),
      igst: 0,
      gstType: "CGST_SGST",
    };
  } else {
    // IGST
    const igst = totalGST;
    return {
      baseAmount: parseFloat(baseAmount.toFixed(2)),
      totalGST: parseFloat(totalGST.toFixed(2)),
      cgst: 0,
      sgst: 0,
      igst: parseFloat(igst.toFixed(2)),
      gstType: "IGST",
    };
  }
}

function calculateOrderAmounts(
  totalAmount,
  drTip,
  isDonorWithinMaharashtra = false,
  isNgoWithinMaharashtra = false
) {
  const baseAmount = totalAmount - drTip;

  // const drTip = baseAmount * DR_TIP_PERCENT;
  const userConvenience = baseAmount * USER_CONVENIENCE_PERCENT;

  // GST Breakdown
  const drTipGST = calculateGSTBreakdown(drTip, isDonorWithinMaharashtra);
  const userConvenienceGST = calculateGSTBreakdown(
    userConvenience,
    isNgoWithinMaharashtra
  );

  return {
    totalAmount: parseFloat(totalAmount.toFixed(2)),
    baseAmount: parseFloat(baseAmount.toFixed(2)),
    drTip: drTip,
    userConvenience: parseFloat(userConvenience.toFixed(2)),

    // DR Tip GST breakdown
    drTipPercent: DR_TIP_PERCENT,
    drTipBaseAmount: drTipGST.baseAmount,
    drTipTotalGST: drTipGST.totalGST,
    drTipCGST: drTipGST.cgst,
    drTipSGST: drTipGST.sgst,
    drTipIGST: drTipGST.igst,
    drTipGSTType: drTipGST.gstType,
    isDonorWithinMaharashtra,

    // User Convenience GST breakdown
    userConveniencePercent: USER_CONVENIENCE_PERCENT,
    userConvenienceBaseAmount: userConvenienceGST.baseAmount,
    userConvenienceTotalGST: userConvenienceGST.totalGST,
    userConvenienceCGST: userConvenienceGST.cgst,
    userConvenienceSGST: userConvenienceGST.sgst,
    userConvenienceIGST: userConvenienceGST.igst,
    userConvenienceGSTType: userConvenienceGST.gstType,
    isNgoWithinMaharashtra,
  };
}

function calculateTransactionAmounts(
  totalAmount,
  baseAmount,
  orderDrTip,
  orderUserConvenience
) {
  // Payment Gateway Charges: 1.18% of total amount
  const paymentGatewayCharges = totalAmount * GATEWAY_CHARGES_PERCENT;

  // Monies Available for Routing
  const moniesAvailableForRouting = totalAmount - paymentGatewayCharges;

  // DR Tip after gateway charges
  const drTipAfterCharges = orderDrTip - orderDrTip * GATEWAY_CHARGES_PERCENT;

  // User Convenience after gateway charges
  const userConvenienceAfterCharges =
    orderUserConvenience - orderUserConvenience * GATEWAY_CHARGES_PERCENT;

  // NGO Platform Convenience Fee
  const ngoPlatformConvenienceFee =
    baseAmount * NGO_PLATFORM_PERCENT - baseAmount * GATEWAY_CHARGES_PERCENT;

  // NGO Net Donation
  const ngoNetDonation = baseAmount - baseAmount * NGO_PLATFORM_PERCENT;

  return {
    paymentGatewayCharges: parseFloat(paymentGatewayCharges.toFixed(2)),
    moniesAvailableForRouting: parseFloat(moniesAvailableForRouting.toFixed(2)),
    drTipAfterCharges: parseFloat(drTipAfterCharges.toFixed(2)),
    userConvenienceAfterCharges: parseFloat(
      userConvenienceAfterCharges.toFixed(2)
    ),
    ngoPlatformConvenienceFee: parseFloat(ngoPlatformConvenienceFee.toFixed(2)),
    ngoNetDonation: parseFloat(ngoNetDonation.toFixed(2)),
  };
}

module.exports = {
  DR_TIP_PERCENT,
  USER_CONVENIENCE_PERCENT,
  GATEWAY_CHARGES_PERCENT,
  NGO_PLATFORM_PERCENT,
  CGST_PERCENT,
  SGST_PERCENT,
  IGST_PERCENT,
  TOTAL_GST_PERCENT,
  calculateOrderAmounts,
  calculateTransactionAmounts,
  calculateGSTBreakdown,
};
