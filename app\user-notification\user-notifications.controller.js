﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
const notificationService = require("./user-notification.service");
const { logAction } = require("../_helpers/logger");

// routes
router.get("/", getAll);
router.get("/notificationConfigList", getAllNotificationConfigList);
router.post("/", notificationSchema, create);
router.get("/:id", getById);
router.put("/:id", notificationSchema, update);
router.delete("/:id", _delete);

module.exports = router;

// controller functions

function create(req, res, next) {
    notificationService
        .create(req.body)
        .then(() => {
            logAction(
                5,
                `Created a user notification in ${req.query.pageName}`
            );
            res.json({
                status: true,
                message: "Notification created successfully",
            });
        })
        .catch(next);
}

function getAllNotificationConfigList(req, res, next) {
    res.json({
        status: true,
        notificationConfigList: [
            "All Notifications",
            "New Campaigns",
            "Events around you",
            "Upcoming Activity",
            "Transactions",
        ],
    });
}

function getAll(req, res, next) {
    notificationService
        .getAll(req.query)
        .then((records) => {
            logAction(
                5,
                `Fetched all user notifications in ${req.query.pageName}`
            );
            res.json(records);
        })
        .catch(next);
}

function getById(req, res, next) {
    notificationService
        .getById(req.params.id)
        .then((record) => res.json(record))
        .catch(next);
}

function update(req, res, next) {
    notificationService
        .update(req.params.id, req.body)
        .then((record) =>
            res.json({ status: true, message: "Notification updated", record })
        )
        .catch(next);
}

function _delete(req, res, next) {
    notificationService
        .delete(req.params.id)
        .then(() =>
            res.json({
                status: true,
                message: "Notification deleted successfully",
            })
        )
        .catch(next);
}

// validation schema
function notificationSchema(req, res, next) {
    const schema = Joi.object({
        title: Joi.string().required(),
        body: Joi.string().required(),
        category_id: Joi.number().optional(),
        type: Joi.string().optional(),
        type_id: Joi.number().optional(),
    });
    validateRequest(req, next, schema);
}
