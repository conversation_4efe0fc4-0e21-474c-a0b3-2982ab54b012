# Split Payment Subscription APIs

This module provides comprehensive subscription management APIs using Razorpay.

## Available Endpoints

### Individual APIs
- `POST /api/payments/createPlan` - Create a subscription plan
- `POST /api/payments/createCustomer` - Create or get a customer
- `POST /api/payments/createSubscription` - Create a subscription
- `POST /api/payments/pauseSubscription` - Pause a subscription
- `POST /api/payments/resumeSubscription` - Resume a subscription
- `POST /api/payments/cancelSubscription` - Cancel a subscription

### Collective API
- `POST /api/payments/setupSubscription` - Complete subscription setup in one call

## Complete Subscription Setup API

The `setupSubscription` endpoint is a collective function that handles the entire subscription setup process in a single API call.

### Endpoint
```
POST /api/payments/setupSubscription
```

### Request Body
```json
{
  // Plan Details (Required)
  "planName": "Monthly Donation Plan",
  "amount": 50000,
  "description": "Monthly donation subscription for NGO support",
  
  // Customer Details (Required)
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "contact": "9876543210",
  
  // Optional Plan Settings
  "period": "monthly",
  "interval": 1,
  "currency": "INR",
  
  // Optional Subscription Settings
  "total_count": 12,
  "quantity": 1,
  "notes": {
    "campaign_id": "123",
    "ngo_id": "456"
  }
}
```

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| planName | string | Name for the subscription plan |
| amount | number | Amount in paise (₹500 = 50000) |
| description | string | Description of the plan |
| name | string | Customer name |
| email | string | Customer email (must match existing user in database) |
| contact | string | Customer mobile number (must match existing user) |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| period | string | "monthly" | Billing period: "daily", "weekly", "monthly", "yearly" |
| interval | number | 1 | Number of periods between charges |
| currency | string | "INR" | Currency code |
| total_count | number | 12 | Total number of billing cycles |
| quantity | number | 1 | Subscription quantity |
| notes | object | {} | Additional metadata |

### Response

#### Success Response
```json
{
  "success": true,
  "message": "Subscription setup completed successfully",
  "data": {
    "plan": {
      "id": "plan_xxxxx",
      "entity": "plan",
      "interval": 1,
      "period": "monthly",
      "item": {
        "id": "item_xxxxx",
        "name": "Monthly Donation Plan",
        "amount": 50000,
        "currency": "INR"
      }
    },
    "customer": {
      "id": "cust_xxxxx",
      "entity": "customer",
      "name": "John Doe",
      "email": "<EMAIL>",
      "contact": "9876543210"
    },
    "subscription": {
      "id": "sub_xxxxx",
      "entity": "subscription",
      "plan_id": "plan_xxxxx",
      "customer_id": "cust_xxxxx",
      "status": "created",
      "total_count": 12,
      "quantity": 1
    },
    "isExistingCustomer": false
  }
}
```

#### Error Response
```json
{
  "success": false,
  "error": "Failed to setup subscription",
  "details": "User not found with provided email and contact"
}
```

### Process Flow

1. **Plan Creation**: Creates a new Razorpay plan with the provided details
2. **Customer Handling**: 
   - Checks if user exists in database with provided email and contact
   - If customer already exists in Razorpay, returns existing customer
   - If new customer, creates Razorpay customer and updates user record
3. **Subscription Creation**: Creates subscription linking the plan and customer

### Error Handling

The API handles various error scenarios:
- Missing required parameters
- User not found in database
- Razorpay API failures
- Network connectivity issues

### Usage Example

```javascript
// Frontend JavaScript example
const setupSubscription = async () => {
  try {
    const response = await fetch('/api/payments/setupSubscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        planName: "Monthly Donation Plan",
        amount: 50000, // ₹500 in paise
        description: "Monthly donation for education support",
        name: "John Doe",
        email: "<EMAIL>",
        contact: "9876543210",
        period: "monthly",
        total_count: 12,
        notes: {
          campaign_id: "123",
          purpose: "education"
        }
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Subscription setup successful:', result.data);
      // Handle success - redirect to payment page or show success message
    } else {
      console.error('Setup failed:', result.error);
      // Handle error - show error message to user
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};
```

### Notes

- The customer's email and contact must match an existing user in the database
- Amount should be provided in paise (multiply rupees by 100)
- The API maintains existing logic and doesn't break any current functionality
- All individual APIs remain available for granular control when needed
