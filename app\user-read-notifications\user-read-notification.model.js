const { DataTypes } = require("sequelize");

module.exports = model;

function model(sequelize) {
  const attributes = {
    user_id: { type: DataTypes.INTEGER, allowNull: false },
    notification_id: { type: DataTypes.INTEGER, allowNull: false },
    isRead: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "no",
    },
  };

  const options = {
    defaultScope: {},
    indexes: [{ unique: false, fields: ["user_id", "notification_id"] }],
  };

  return sequelize.define("user_read_notifications", attributes, options);
}
