﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const productsService = require("./product.service");
const multer = require("multer");
const { logAction } = require("../_helpers/logger");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let dir = __basedir + "uploads/product-images";
    cb(null, __basedir + "uploads/product-images/");
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  },
});

// STAND ALONE CONFIG
const uploadConfig = multer({
  storage: storage,
});

// routes
router.get("/", getAll);
router.post("/", uploadConfig.single("file"), create);
router.get("/:id", getById);
router.put("/:id", uploadConfig.single("file"), updateSchema, update);
router.patch("/:id", patch);
router.delete("/:id", _delete);
router.delete("/byNgoId/:id", deleteByNgoId);

module.exports = router;

function create(req, res, next) {
  productsService
    .create(req.body)
    .then(() => {
      logRequest(
        req,
        `Created a new product: ${req.body.name}, NGO ID: ${req.body.ngo_id}, Discounted Price: ${req.body.discountedPrice}`,
        "CREATE"
      );
      res.json({ status: true, message: "Record created successfully" });
    })
    .catch(next);
}

function getAll(req, res, next) {
  const { page, limit, ngoId } = req.query;
  if (ngoId) {
    productsService
      .getAll(ngoId, page, limit)
      .then((records) => {
        logRequest(req, "Fetched all products by NGO ID", "READ");
        res.json(records);
      })
      .catch(next);
  } else {
    productsService
      .getByNgoId(req.query, page, limit)
      .then((records) => {
        logRequest(req, "Fetched all products", "READ");
        res.json(records);
      })
      .catch(next);
  }
}

function getById(req, res, next) {
  productsService
    .getById(req.params.id)
    .then((product) => {
      logRequest(
        req,
        `Fetched product: ${product?.name}, NGO ID: ${product?.ngo_id}, Discounted Price: ${product?.discountedPrice}`,
        "READ"
      );
      res.json(product);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    name: Joi.string().required(),
    description: Joi.string().required(),
    isStock: Joi.string().required(),
    discountedPrice: Joi.number().allow(null, ""),
    discount: Joi.number().precision(2).allow(null, ""),
    imageName: Joi.string().optional(),
    ngo_id: Joi.optional().allow(null),
    count: Joi.number().integer().required(),
    status: Joi.string().allow(null, ""),
    collection_id: Joi.optional().allow(null),
    price: Joi.number().required(),
    unit_of_measure: Joi.optional().allow(null),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  productsService
    .update(req.params.id, req.body)
    .then((product) => {
      logRequest(
        req,
        `Updated product: ${product?.name}, NGO ID: ${product?.ngo_id}, Discounted Price: ${product?.discountedPrice}`,
        "UPDATE"
      );
      res.json(product);
    })
    .catch(next);
}

function patch(req, res, next) {
  productsService
    .patch(req.params.id, req.body)
    .then((product) => {
      logRequest(
        req,
        `Patched product: ${product?.name}, NGO ID: ${product?.ngo_id}, Discounted Price: ${product?.discountedPrice}`,
        "UPDATE"
      );
      res.json(product);
    })
    .catch(next);
}

function _delete(req, res, next) {
  productsService
    .delete(req.params.id)
    .then(() => {
      logRequest(req, `Deleted product with ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
function deleteByNgoId(req, res, next) {
  productsService
    .deleteByNgoId(req.params.id)
    .then(() => {
      logRequest(req, `Deleted products by NGO ID: ${req.params.id}`, "DELETE");
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}
