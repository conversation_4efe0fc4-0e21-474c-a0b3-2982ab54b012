const express = require("express");
const config = require("../../config.json");
const axios = require("axios");
const Razorpay = require("razorpay");
const crypto = require("crypto");

const router = express.Router();

// Routes
router.post("/create-order", createOrder);
router.post("/capture", capture);
router.post("/transfer", transfer);
router.post("/refund", refund);
router.post("/verifyPayment", verifyPayment);

module.exports = router;

const KEY_ID = config.RAZORPAY_KEY_ID;
const KEY_SECRET = config.RAZORPAY_KEY_SECRET;
const auth = Buffer.from(`${KEY_ID}:${KEY_SECRET}`).toString("base64");

async function createOrder(req, res, next) {
  const { amount, currency = "INR", receipt, notes } = req.body;

  try {
    const razorpay = new Razorpay({
      key_id: KEY_ID,
      key_secret: KEY_SECRET,
    });

    const options = {
      amount: Number(amount) * 100, // Ra<PERSON>pay expects amount in paise
      currency,
      receipt: receipt || `rcptid_${Date.now()}`,
      payment_capture: 1, // Auto-capture
      notes: notes || {},
    };

    const order = await razorpay.orders.create(options);

    const callbackUrl = `http://localhost:4000/api/payments/verifyPayment?orderId=39`;

    const params = new URLSearchParams({
      key_id: KEY_ID,
      amount: (Number(amount) * 100).toString(),
      currency: "INR",
      name: "Your App Name",
      order_id: order.id,
      redirect: "true",
      callback_url: callbackUrl,
    });

    const paymentUrl = `https://api.razorpay.com/v1/checkout/embedded?${params.toString()}`;

    return res.json({ success: true, order });
  } catch (error) {
    console.error("Create order error:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to create order",
      details: error.message,
    });
  }
}

async function verifyPayment(req, res) {
  const { orderId } = req.query;
  const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
    req.body;

  const expectedSignature = crypto
    .createHmac("sha256", KEY_SECRET)
    .update(`${razorpay_order_id}|${razorpay_payment_id}`)
    .digest("hex");

  // const mobileOS = utils.getMobileOS();

  if (expectedSignature === razorpay_signature) {
    await db.Order.update(
      {
        payment_status: "Confirmed",
        paymentId: razorpay_payment_id,
      },
      { where: { id: orderId } }
    );

    const transactionRecord = await db.Transaction.findOne({
      where: { order_id: orderId },
    });

    if (transactionRecord) {
      let impact_created = 0;

      // Only calculate impact for genericViaMoney
      if (
        transactionRecord.donation_type === "genericViaMoney" &&
        transactionRecord.bucket_id
      ) {
        const bucketItems = await db.BucketItem.findAll({
          where: { bucket_id: transactionRecord.bucket_id },
          order: [["id", "DESC"]],
        });

        for (const item of bucketItems) {
          if (!item.category_id || !item.percentage) continue;

          const category = await db.Category.findByPk(item.category_id);
          if (!category || !category.ratio) continue;

          const partialImpact =
            (Number(transactionRecord.total_price) * Number(category.ratio)) /
            Number(item.percentage);
          impact_created += partialImpact;
        }

        const user = await db.User.findByPk(transactionRecord.user_id);
        if (user) {
          const previousImpact = Number(user.total_impact_created) || 0;
          const newImpact = previousImpact + impact_created;
          await user.update({ total_impact_created: newImpact });
        }
      } else if (
        transactionRecord.donation_type === "ngo" &&
        transactionRecord.ngo_id
      ) {
        const ngoCategory = await db.NgoCategory.findOne({
          where: { ngo_id: transactionRecord.ngo_id },
        });

        if (ngoCategory && ngoCategory.category_id) {
          const category = await db.Category.findByPk(ngoCategory.category_id);
          if (category && category.ratio) {
            const partialImpact =
              (Number(transactionRecord.total_price) * Number(category.ratio)) /
              100;
            impact_created += partialImpact;
          }
        }
      } else if (
        transactionRecord.donation_type === "campaign" &&
        transactionRecord.campaign_id
      ) {
        const campaign = await db.Campaign.findByPk(
          transactionRecord.campaign_id
        );

        if (campaign && campaign.category_id) {
          const category = await db.Category.findByPk(campaign.category_id);
          if (category && category.ratio) {
            const partialImpact =
              (Number(transactionRecord.total_price) * Number(category.ratio)) /
              100;
            impact_created += partialImpact;
          }
        }
      }

      // Update transaction with payment info + impact_created
      await db.Transaction.update(
        {
          razorpay_payment_id,
          all_values: JSON.stringify(req.body),
          status: "captured",
          impact_created, // <-- new addition here
        },
        { where: { id: transactionRecord.id } }
      );
    }
    res.send(`
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Payment Successful</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    :root {
      --primary-color: #ffaa4c;
      --primary-light: rgba(255, 170, 76, 0.85);
      --primary-dark: #f59000;
      --white: #ffffff;
      --light-bg: #fff8f0;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-dark) 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .background-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      overflow: hidden;
    }
    
    .shape {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }
    
    .shape-1 {
      width: 300px;
      height: 300px;
      top: -150px;
      left: -100px;
    }
    
    .shape-2 {
      width: 200px;
      height: 200px;
      bottom: -100px;
      right: -50px;
    }
    
    .shape-3 {
      width: 150px;
      height: 150px;
      top: 50%;
      right: 10%;
    }
    
    .logo {
      position: fixed;
      top: 20px;
      left: 20px;
      width: 120px;
      height: auto;
      z-index: 10;
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      transition: transform 0.3s ease;
    }
    
    .logo:hover {
      transform: scale(1.05);
    }
    
    .card {
      background-color: var(--white);
      border-radius: 16px;
      padding: 40px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 20;
      animation: fadeIn 0.8s ease-out, floatUp 0.8s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes floatUp {
      from { transform: translateY(30px); }
      to { transform: translateY(0); }
    }
    
    .success-icon {
      width: 80px;
      height: 80px;
      background-color: var(--light-bg);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
    }
    
    .success-icon svg {
      width: 40px;
      height: 40px;
      color: var(--primary-color);
    }
    
    h2 {
      color: #333;
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 3px;
      width: 60px;
      background-color: var(--primary-color);
      margin: 0 auto 24px;
      border-radius: 3px;
    }
    
    p {
      color: #666;
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    button {
      padding: 14px 32px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--primary-color);
      color: var(--white);
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: 0 4px 12px rgba(255, 170, 76, 0.3);
    }
    
    button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 170, 76, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    @media (max-width: 600px) {
      .card {
        padding: 30px 20px;
      }
      
      h2 {
        font-size: 24px;
      }
      
      p {
        font-size: 15px;
      }
      
      .success-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
      }
      
      .success-icon svg {
        width: 35px;
        height: 35px;
      }
      
      button {
        padding: 12px 24px;
        font-size: 15px;
      }
      
      .logo {
        width: 90px;
        top:-20px;
        left: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>
  
  <img src="http://localhost:4000/api/fetchMultipleCampaignImages/do-right-logo.png" alt="DoRight Logo" class="logo" />
  
  <div class="card">
    <div class="success-icon">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    </div>
    
    <h2>Transaction Complete</h2>
    <div class="divider"></div>
    
    <p>
      Thank you for your support.<br />
      Your action has been successfully processed.<br />
      We appreciate your involvement and interest in our initiative.
    </p>
    
  </div>
  
  <script>
    // Add a small animation to the success icon
    document.addEventListener('DOMContentLoaded', function() {
      const successIcon = document.querySelector('.success-icon');
      setTimeout(() => {
        successIcon.style.transform = 'scale(1.1)';
        setTimeout(() => {
          successIcon.style.transform = 'scale(1)';
        }, 200);
      }, 500);
    });
  </script>
</body>

</html>
`);

    // res.redirect(
    //   `doright://paymentCallback?status=paymentSuccess&orderId=${orderId}`
    // );
    return;
    // res.json({ success: true, message: "Payment verified" });
  } else {
    // res.status(400).json({ success: false, error: "Invalid signature });
    // window.location.href = `doright://paymentCallback?status=paymentFailed&orderId=${razorpay_order_id}`;

    await db.Order.update(
      {
        payment_status: "failed",
        paymentId: razorpay_payment_id,
      },
      { where: { id: orderId } }
    );

    const transactionRecord = await db.Transaction.findOne({
      where: { order_id: orderId },
    });

    if (transactionRecord) {
      await db.Transaction.update(
        {
          razorpay_payment_id: razorpay_payment_id,
          all_values: JSON.stringify(req.body),
          status: "failed",
        },
        { where: { id: transactionRecord.id } }
      );
    }
    res.send(`
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Payment Failed</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    :root {
      --primary-color: #ffaa4c;
      --primary-light: rgba(255, 170, 76, 0.85);
      --primary-dark: #f59000;
      --error-color: #ff6b6b;
      --error-dark: #e05050;
      --white: #ffffff;
      --light-bg: #fff8f0;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-dark) 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .background-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      overflow: hidden;
    }
    
    .shape {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }
    
    .shape-1 {
      width: 300px;
      height: 300px;
      top: -150px;
      left: -100px;
    }
    
    .shape-2 {
      width: 200px;
      height: 200px;
      bottom: -100px;
      right: -50px;
    }
    
    .shape-3 {
      width: 150px;
      height: 150px;
      top: 50%;
      right: 10%;
    }
    
    .logo {
      position: fixed;
      top: 20px;
      left: 20px;
      width: 120px;
      height: auto;
      z-index: 10;
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      transition: transform 0.3s ease;
    }
    
    .logo:hover {
      transform: scale(1.05);
    }
    
    .card {
      background-color: var(--white);
      border-radius: 16px;
      padding: 40px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 20;
      animation: fadeIn 0.8s ease-out, floatUp 0.8s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes floatUp {
      from { transform: translateY(30px); }
      to { transform: translateY(0); }
    }
    
    .error-icon {
      width: 80px;
      height: 80px;
      background-color: rgba(255, 107, 107, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
    }
    
    .error-icon svg {
      width: 40px;
      height: 40px;
      color: var(--error-color);
    }
    
    h2 {
      color: #333;
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 3px;
      width: 60px;
      background-color: var(--error-color);
      margin: 0 auto 24px;
      border-radius: 3px;
    }
    
    p {
      color: #666;
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    button {
      padding: 14px 32px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--primary-color);
      color: var(--white);
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: 0 4px 12px rgba(255, 170, 76, 0.3);
    }
    
    button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 170, 76, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    @media (max-width: 600px) {
      .card {
        padding: 30px 20px;
      }
      
      h2 {
        font-size: 24px;
      }
      
      p {
        font-size: 15px;
      }
      
      .error-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
      }
      
      .error-icon svg {
        width: 35px;
        height: 35px;
      }
      
      button {
        padding: 12px 24px;
        font-size: 15px;
      }
      
      .logo {
        width: 90px;
        top: 15px;
        left: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>
  
  <img src="http://localhost:4000/api/fetchMultipleCampaignImages/do-right-logo.png" alt="DoRight Logo" class="logo" />
  
  <div class="card">
    <div class="error-icon">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </div>
    
    <h2>Something Went Wrong</h2>
    <div class="divider"></div>
    
    <p>
      We couldn't complete your request.<br />
      Please try again later or contact support if the problem persists.
    </p>
    
    <button onclick="window.close()">Return to App</button>
  </div>
  
  <script>
    // Add a small animation to the error icon
    document.addEventListener('DOMContentLoaded', function() {
      const errorIcon = document.querySelector('.error-icon');
      setTimeout(() => {
        errorIcon.style.transform = 'scale(1.1)';
        setTimeout(() => {
          errorIcon.style.transform = 'scale(1)';
        }, 200);
      }, 500);
    });
  </script>
</body>
</html>

`);

    return;
  }
}

// Capture Payment
async function capture(req, res, next) {
  const { paymentId, amount } = req.body;
  if (!amount || amount <= 0) {
    return res.status(400).json({ success: false, error: "Invalid amount" });
  }

  axios
    .post(
      `https://api.razorpay.com/v1/payments/${paymentId}/capture`,
      { amount: amount || 0, currency: "INR" },
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    )
    .then((response) => {
      res.json({ success: true, data: response.data });
    })
    .catch((error) => {
      console.error("Capture error:", error.response?.data || error.message);
      res.status(500).json({
        error: "Payment capture failed",
        details: error.response?.data || error.message,
      });
    });
}

// Transfer Payment
async function transfer(req, res, next) {
  const { paymentId } = req.body;

  const transferData = {
    transfers: [
      {
        account: "acc_PpImPzYY1ctZC7",
        amount: 1000,
        currency: "INR",
        notes: {
          branch: "ADYAR SHAYADRI CAMPUS",
          name: "Test Private Limited",
        },
      },
      {
        account: "acc_PpIgQv2w5MDAZe",
        amount: 1000,
        currency: "INR",
        notes: {
          branch: "ADYAR SHAYADRI CAMPUS",
          name: "Test Business",
        },
      },
    ],
  };

  axios
    .post(
      `https://api.razorpay.com/v1/payments/${paymentId}/transfers`,
      transferData,
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    )
    .then((response) => {
      res.json({ success: true, data: response.data });
    })
    .catch((error) => {
      console.error("Transfer error:", error.response?.data || error.message);
      res.status(500).json({
        error: "Transfer failed",
        details: error.response?.data || error.message,
      });
    });
}

// Refund Payment
async function refund(req, res, next) {
  const { paymentId } = req.body;

  axios
    .post(
      `https://api.razorpay.com/v1/payments/${paymentId}/refund`,
      { amount: 2000, currency: "INR" },
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    )
    .then((response) => {
      res.json({ success: true, data: response.data });
    })
    .catch((error) => {
      console.error("Refund error:", error.response?.data || error.message);
      res.status(500).json({
        error: "Refund failed",
        details: error.response?.data || error.message,
      });
    });
}
