const express = require("express");
const config = require("../../config.json");
const axios = require("axios");
const Razorpay = require("razorpay");
const crypto = require("crypto");
const db = require("../_helpers/db");
const splitPaymentService = require("./split-payment.service");

const router = express.Router();

// Routes
router.post("/create-order", createOrder);
router.post("/capture", capture);
// router.post("/transfer", transfer);
router.post("/refund", refund);
router.post("/verifyPayment", verifyPayment);
router.post("/createRazorpayAccount", onboardNgoToRazorpay);
router.post("/createPlan", createPlan);
router.post("/createCustomer", createCustomer);
router.post("/createSubscription", createSubscription);
router.post("/setupSubscription", setupSubscription);
router.post("/pauseSubscription", pauseSubscription);
router.post("/resumeSubscription", resumeSubscription);
router.post("/cancelSubscription", cancelSubscription);
router.get("/subscription/:id", getSubscriptionStatus);

// router.post(
//   "/razorpayWebhook",
//   express.raw({ type: "application/json" }),
//   razorpayWebhook
// );

module.exports = router;

const KEY_ID = config.RAZORPAY_KEY_ID;
const KEY_SECRET = config.RAZORPAY_KEY_SECRET;
const auth = Buffer.from(`${KEY_ID}:${KEY_SECRET}`).toString("base64");
const razorpay = new Razorpay({ key_id: KEY_ID, key_secret: KEY_SECRET });

async function createOrder(req, res, next) {
  const { amount, currency = "INR", receipt, notes } = req.body;

  try {
    const razorpay = new Razorpay({
      key_id: KEY_ID,
      key_secret: KEY_SECRET,
    });

    const options = {
      amount: Number(amount) * 100, // Razorpay expects amount in paise
      currency,
      receipt: receipt || `rcptid_${Date.now()}`,
      payment_capture: 1, // Auto-capture
      notes: notes || {},
    };

    const order = await razorpay.orders.create(options);

    const callbackUrl = `http://localhost:4000/api/payments/verifyPayment?orderId=39`;

    const params = new URLSearchParams({
      key_id: KEY_ID,
      amount: (Number(amount) * 100).toString(),
      currency: "INR",
      name: "Your App Name",
      order_id: order.id,
      redirect: "true",
      callback_url: callbackUrl,
    });

    const paymentUrl = `https://api.razorpay.com/v1/checkout/embedded?${params.toString()}`;

    return res.json({ success: true, order });
  } catch (error) {
    console.error("Create order error:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to create order",
      details: error.message,
    });
  }
}

async function verifyPayment(req, res) {
  const { orderId } = req.query;
  const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
    req.body;

  const expectedSignature = crypto
    .createHmac("sha256", KEY_SECRET)
    .update(`${razorpay_order_id}|${razorpay_payment_id}`)
    .digest("hex");

  // const mobileOS = utils.getMobileOS();

  if (expectedSignature === razorpay_signature) {
    await db.Order.update(
      {
        payment_status: "Confirmed",
        paymentId: razorpay_payment_id,
      },
      { where: { id: orderId } }
    );

    const transactionRecord = await db.Transaction.findOne({
      where: { order_id: orderId },
    });

    if (transactionRecord) {
      let impact_created = 0;

      const user = await db.User.findByPk(transactionRecord.user_id);

      // Only calculate impact for genericViaMoney
      if (
        transactionRecord.donation_type === "genericViaMoney" &&
        transactionRecord.bucket_id
      ) {
        // Step 1: Get the order info
        const order = await db.Order.findByPk(transactionRecord.order_id);
        if (!order || !order.price_per_unit) return;

        const pricePerUnit = Number(order.price_per_unit);

        // Step 2: Get all bucket items
        const bucketItems = await db.BucketItem.findAll({
          where: { bucket_id: transactionRecord.bucket_id },
          order: [["id", "DESC"]],
        });

        for (const item of bucketItems) {
          if (!item.category_id || !item.percentage || !item.campaign_id)
            continue;

          const category = await db.Category.findByPk(item.category_id);
          if (!category || !category.ratio) continue;

          const partialImpact =
            (pricePerUnit * Number(category.ratio)) / Number(item.percentage);
          impact_created += partialImpact;

          // === Step 3: Update Campaign Stats ===
          const campaign = await db.Campaign.findByPk(item.campaign_id);
          if (campaign) {
            const prevAmount = Number(campaign.amount_raised) || 0;
            const prevBackers = Number(campaign.backers) || 0;
            const fundTarget = Number(campaign.fund_raising_target) || 0;

            const newAmount = prevAmount + pricePerUnit;
            const newBackers = prevBackers + 1;
            const goalPercentage =
              fundTarget > 0
                ? Math.min((newAmount / fundTarget) * 100, 100).toFixed(2)
                : 0;

            await campaign.update({
              amount_raised: newAmount,
              backers: newBackers,
              goal_percentage_achieved: goalPercentage,
            });
          }
        }

        // Step 4: Update user's total impact
        if (user) {
          const previousImpact = Number(user.total_impact_created) || 0;
          const newImpact = previousImpact + impact_created;
          await user.update({ total_impact_created: newImpact });
        }
      } else if (
        transactionRecord.donation_type === "ngo" &&
        transactionRecord.ngo_id
      ) {
        const ngoCategory = await db.NgoCategory.findOne({
          where: { ngo_id: transactionRecord.ngo_id },
        });

        if (ngoCategory && ngoCategory.category_id) {
          const category = await db.Category.findByPk(ngoCategory.category_id);
          if (category && category.ratio) {
            const partialImpact =
              (Number(transactionRecord.amount) * Number(category.ratio)) / 100;
            impact_created += partialImpact;
          }
        }
        if (user) {
          const previousImpact = Number(user.total_impact_created) || 0;
          const newImpact = previousImpact + impact_created;
          await user.update({ total_impact_created: newImpact });
        }
      } else if (
        transactionRecord.donation_type === "campaign" &&
        transactionRecord.campaign_id
      ) {
        const campaign = await db.Campaign.findByPk(
          transactionRecord.campaign_id
        );
        let impact_created = 0;

        if (campaign && campaign.category_id) {
          const category = await db.Category.findByPk(campaign.category_id);
          if (category && category.ratio) {
            const partialImpact =
              (Number(transactionRecord.amount) * Number(category.ratio)) / 100;
            impact_created += partialImpact;
          }

          // === Update Campaign Fields ===
          const prevAmountRaised = Number(campaign.amount_raised) || 0;
          const prevBackers = Number(campaign.backers) || 0;
          const fundTarget = Number(campaign.fund_raising_target) || 0;

          const newAmountRaised =
            prevAmountRaised + Number(transactionRecord.amount);
          const newBackers = prevBackers + 1;

          const percentageAchieved =
            fundTarget > 0
              ? ((newAmountRaised / fundTarget) * 100).toFixed(2)
              : 0;

          await campaign.update({
            amount_raised: newAmountRaised,
            backers: newBackers,
            goal_percentage_achieved: percentageAchieved,
          });
        }

        if (user) {
          const previousImpact = Number(user.total_impact_created) || 0;
          const newImpact = previousImpact + impact_created;
          await user.update({ total_impact_created: newImpact });
        }
      }

      // Update transaction with payment info + impact_created
      await db.Transaction.update(
        {
          razorpay_payment_id,
          all_values: JSON.stringify(req.body),
          status: "captured",
          impact_created, // <-- new addition here
        },
        { where: { id: transactionRecord.id } }
      );
      await transferPayment(razorpay_payment_id, transactionRecord.amount);
    }
    res.send(`
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Payment Successful</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    :root {
      --primary-color: #ffaa4c;
      --primary-light: rgba(255, 170, 76, 0.85);
      --primary-dark: #f59000;
      --white: #ffffff;
      --light-bg: #fff8f0;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-dark) 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .background-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      overflow: hidden;
    }
    
    .shape {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }
    
    .shape-1 {
      width: 300px;
      height: 300px;
      top: -150px;
      left: -100px;
    }
    
    .shape-2 {
      width: 200px;
      height: 200px;
      bottom: -100px;
      right: -50px;
    }
    
    .shape-3 {
      width: 150px;
      height: 150px;
      top: 50%;
      right: 10%;
    }
    
    .logo {
      position: fixed;
      top: 20px;
      left: 20px;
      width: 120px;
      height: auto;
      z-index: 10;
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      transition: transform 0.3s ease;
    }
    
    .logo:hover {
      transform: scale(1.05);
    }
    
    .card {
      background-color: var(--white);
      border-radius: 16px;
      padding: 40px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 20;
      animation: fadeIn 0.8s ease-out, floatUp 0.8s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes floatUp {
      from { transform: translateY(30px); }
      to { transform: translateY(0); }
    }
    
    .success-icon {
      width: 80px;
      height: 80px;
      background-color: var(--light-bg);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
    }
    
    .success-icon svg {
      width: 40px;
      height: 40px;
      color: var(--primary-color);
    }
    
    h2 {
      color: #333;
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 3px;
      width: 60px;
      background-color: var(--primary-color);
      margin: 0 auto 24px;
      border-radius: 3px;
    }
    
    p {
      color: #666;
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    button {
      padding: 14px 32px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--primary-color);
      color: var(--white);
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: 0 4px 12px rgba(255, 170, 76, 0.3);
    }
    
    button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 170, 76, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    @media (max-width: 600px) {
      .card {
        padding: 30px 20px;
      }
      
      h2 {
        font-size: 24px;
      }
      
      p {
        font-size: 15px;
      }
      
      .success-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
      }
      
      .success-icon svg {
        width: 35px;
        height: 35px;
      }
      
      button {
        padding: 12px 24px;
        font-size: 15px;
      }
      
      .logo {
        width: 90px;
        top:-20px;
        left: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>
  
  <img src="http://localhost:4000/api/fetchMultipleCampaignImages/do-right-logo.png" alt="DoRight Logo" class="logo" />
  
  <div class="card">
    <div class="success-icon">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    </div>
    
    <h2>Transaction Complete</h2>
    <div class="divider"></div>
    
    <p>
      Thank you for your support.<br />
      Your action has been successfully processed.<br />
      We appreciate your involvement and interest in our initiative.
    </p>
        <p><strong>Click to close and return to app.<strong></p>

  </div>
  
  <script>
    // Add a small animation to the success icon
    document.addEventListener('DOMContentLoaded', function() {
      const successIcon = document.querySelector('.success-icon');
      setTimeout(() => {
        successIcon.style.transform = 'scale(1.1)';
        setTimeout(() => {
          successIcon.style.transform = 'scale(1)';
        }, 200);
      }, 500);
    });
  </script>
</body>

</html>
`);

    // res.redirect(
    //   `doright://paymentCallback?status=paymentSuccess&orderId=${orderId}`
    // );
    return;
    // res.json({ success: true, message: "Payment verified" });
  } else {
    // res.status(400).json({ success: false, error: "Invalid signature });
    // window.location.href = `doright://paymentCallback?status=paymentFailed&orderId=${razorpay_order_id}`;

    await db.Order.update(
      {
        payment_status: "failed",
        paymentId: razorpay_payment_id,
      },
      { where: { id: orderId } }
    );

    const transactionRecord = await db.Transaction.findOne({
      where: { order_id: orderId },
    });

    if (transactionRecord) {
      await db.Transaction.update(
        {
          razorpay_payment_id: razorpay_payment_id,
          all_values: JSON.stringify(req.body),
          status: "failed",
        },
        { where: { id: transactionRecord.id } }
      );
    }
    res.send(`
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Payment Failed</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    :root {
      --primary-color: #ffaa4c;
      --primary-light: rgba(255, 170, 76, 0.85);
      --primary-dark: #f59000;
      --error-color: #ff6b6b;
      --error-dark: #e05050;
      --white: #ffffff;
      --light-bg: #fff8f0;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-dark) 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .background-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      overflow: hidden;
    }
    
    .shape {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }
    
    .shape-1 {
      width: 300px;
      height: 300px;
      top: -150px;
      left: -100px;
    }
    
    .shape-2 {
      width: 200px;
      height: 200px;
      bottom: -100px;
      right: -50px;
    }
    
    .shape-3 {
      width: 150px;
      height: 150px;
      top: 50%;
      right: 10%;
    }
    
    .logo {
      position: fixed;
      top: 20px;
      left: 20px;
      width: 120px;
      height: auto;
      z-index: 10;
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      transition: transform 0.3s ease;
    }
    
    .logo:hover {
      transform: scale(1.05);
    }
    
    .card {
      background-color: var(--white);
      border-radius: 16px;
      padding: 40px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 20;
      animation: fadeIn 0.8s ease-out, floatUp 0.8s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes floatUp {
      from { transform: translateY(30px); }
      to { transform: translateY(0); }
    }
    
    .error-icon {
      width: 80px;
      height: 80px;
      background-color: rgba(255, 107, 107, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
    }
    
    .error-icon svg {
      width: 40px;
      height: 40px;
      color: var(--error-color);
    }
    
    h2 {
      color: #333;
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 16px;
    }
    
    .divider {
      height: 3px;
      width: 60px;
      background-color: var(--error-color);
      margin: 0 auto 24px;
      border-radius: 3px;
    }
    
    p {
      color: #666;
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 30px;
    }
    
    button {
      padding: 14px 32px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--primary-color);
      color: var(--white);
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      box-shadow: 0 4px 12px rgba(255, 170, 76, 0.3);
    }
    
    button:hover {
      background-color: var(--primary-dark);
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(255, 170, 76, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    @media (max-width: 600px) {
      .card {
        padding: 30px 20px;
      }
      
      h2 {
        font-size: 24px;
      }
      
      p {
        font-size: 15px;
      }
      
      .error-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
      }
      
      .error-icon svg {
        width: 35px;
        height: 35px;
      }
      
      button {
        padding: 12px 24px;
        font-size: 15px;
      }
      
      .logo {
        width: 90px;
        top: 15px;
        left: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>
  
  <img src="http://localhost:4000/api/fetchMultipleCampaignImages/do-right-logo.png" alt="DoRight Logo" class="logo" />
  
  <div class="card">
    <div class="error-icon">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </div>
    
    <h2>Something Went Wrong</h2>
    <div class="divider"></div>
    
    <p>
      We couldn't complete your request.<br />
      Please try again later or contact support if the problem persists.
      
    </p>
    <p><strong>Click to close and return to app.<strong></p>
    
    
  </div>
  
  <script>
    // Add a small animation to the error icon
    document.addEventListener('DOMContentLoaded', function() {
      const errorIcon = document.querySelector('.error-icon');
      setTimeout(() => {
        errorIcon.style.transform = 'scale(1.1)';
        setTimeout(() => {
          errorIcon.style.transform = 'scale(1)';
        }, 200);
      }, 500);
    });
  </script>
</body>
</html>

`);

    return;
  }
}

// Capture Payment
async function capture(req, res, next) {
  const { paymentId, amount } = req.body;
  if (!amount || amount <= 0) {
    return res.status(400).json({ success: false, error: "Invalid amount" });
  }

  axios
    .post(
      `https://api.razorpay.com/v1/payments/${paymentId}/capture`,
      { amount: amount || 0, currency: "INR" },
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    )
    .then((response) => {
      res.json({ success: true, data: response.data });
    })
    .catch((error) => {
      console.error("Capture error:", error.response?.data || error.message);
      res.status(500).json({
        error: "Payment capture failed",
        details: error.response?.data || error.message,
      });
    });
}

// Transfer Payment
async function transferPayment(paymentId, amount) {
  // const { paymentId, amount } = req.body;
  const ngoAmount = (Number(amount) - 100) * 100;
  const processingFee = 100 * 100;
  const transferData = {
    transfers: [
      {
        account: "acc_QiJIKicDVleHrS",
        amount: ngoAmount,
        currency: "INR",
        notes: {
          branch: "ADYAR SHAYADRI CAMPUS",
          name: "Test Private Limited",
        },
      },
      {
        account: "acc_QiLPVlZleBvOtV",
        amount: processingFee,
        currency: "INR",
        notes: {
          branch: "ADYAR SHAYADRI CAMPUS",
          name: "Test Business",
        },
      },
    ],
  };

  axios
    .post(
      `https://api.razorpay.com/v1/payments/${paymentId}/transfers`,
      transferData,
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    )
    .then((response) => {
      console.error("response", response.data);
      return response.data;
      // res.json({ success: true, data: response.data });
    })
    .catch((error) => {
      console.error("Transfer error:", error.response?.data || error.message);
      return error?.response?.data;
      // res.status(500).json({
      //   error: "Transfer failed",
      //   details: error.response?.data || error.message,
      // });
    });
}

// Refund Payment
async function refund(req, res, next) {
  const { paymentId } = req.body;

  axios
    .post(
      `https://api.razorpay.com/v1/payments/${paymentId}/refund`,
      { amount: 2000, currency: "INR" },
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    )
    .then((response) => {
      res.json({ success: true, data: response.data });
    })
    .catch((error) => {
      console.error("Refund error:", error.response?.data || error.message);
      res.status(500).json({
        error: "Refund failed",
        details: error.response?.data || error.message,
      });
    });
}

async function onboardNgoToRazorpay(req, res, next) {
  const { ngo_id, account_number, ifsc_code, beneficiary_name } = req.body;
  try {
    const requestBody = await buildAccountRequestBody(ngo_id);
    // 1. Create Razorpay Account
    const accountResponse = await axios.post(
      "https://api.razorpay.com/v2/accounts",
      requestBody,
      {
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
      }
    );
    const accountId = accountResponse.data.id;

    // 2. Create Stakeholder
    // const stakeholderResponse = await createRazorpayStakeholder(
    //   ngo_id,
    //   accountId
    // );

    // 3. Enable Route Product
    const productResponse = await enableRouteProduct(accountId);
    const productId = productResponse?.id || productResponse?.items?.[0]?.id;

    // 4. Configure Settlements
    const settlementResponse = await configureRazorpaySettlements({
      accountId,
      productId,
      account_number,
      ifsc_code,
      beneficiary_name,
    });

    await db.BankDetail.update(
      {
        accountResponse: JSON.stringify(accountResponse.data),
        // stakeholderResponse: JSON.stringify(stakeholderResponse),
        productResponse: JSON.stringify(productResponse),
        settlementResponse: JSON.stringify(settlementResponse),
        accountId: accountId,
        productId: productId,
      },
      { where: { ngo_id } }
    );

    res.json({
      success: true,
      data: {
        razorpay_account_id: accountId,
        // stakeholder: stakeholderResponse,
        product_enabled: productResponse,
        settlement_configured: settlementResponse,
      },
    });
  } catch (error) {
    console.error(
      "Razorpay onboarding error:",
      error.response?.data || error.message
    );
    res.status(500).json({
      status: false,
      error: "Razorpay onboarding error",
      details: error.response?.data || error.message,
    });
  }
}

async function buildAccountRequestBody(ngo_id) {
  const ngo = await db.Ngo.findByPk(ngo_id);

  if (!ngo) throw new Error("NGO not found");

  return {
    email: ngo.email,
    phone: ngo.point_of_contact_mobile_number || "**********",
    type: "route",
    reference_id: `${ngo.id}_${Date.now()}`,
    legal_business_name: ngo.name,
    business_type: "ngo",
    contact_name: ngo.point_of_contact_name || ngo.name,
    profile: {
      category: "not_for_profit",
      subcategory: "charity",
      addresses: {
        registered: {
          street1: ngo.current_address || "Unknown",
          street2: `${ngo.state} ${ngo.pincode}`,
          city: ngo.place_name || "Pune",
          state: ngo.state || "Maharashtra",
          postal_code: ngo.pincode || 411001,
          country: getValidCountryCode(ngo.country) || "IN",
        },
      },
    },
    legal_info: {
      pan: "",
      gst: "",
    },
  };
}

async function createRazorpayStakeholder(ngo_id, accountId) {
  try {
    // 1. Fetch NGO from DB
    const ngo = await db.Ngo.findByPk(ngo_id);

    if (!ngo) {
      return "NGO not found";
    }

    // 2. Build request body for stakeholder creation
    const requestBody = {
      name: ngo.point_of_contact_name || ngo.name,
      email: ngo.email,
      addresses: {
        residential: {
          // street: ngo.current_address || "Pune , Maharashtra ",
          street: "Pune , Maharashtra ",
          city: ngo.place_name || "Pune",
          state: ngo.state || "Maharashtra",
          postal_code: ngo.pincode || "000000",
          country: getValidCountryCode(ngo.country) || "IN",
        },
      },
      kyc: {
        // pan: ngo.pan || "",
        pan: "",
      },
      notes: {
        random_key: Math.random().toString(36).substring(2, 10), // e.g., "k39sjf7a"
      },
    };

    // 3. POST to Razorpay stakeholders API
    const response = await axios.post(
      `https://api.razorpay.com/v2/accounts/${accountId}/stakeholders`,
      requestBody,
      {
        headers: {
          Authorization: `Basic ${auth}`,
          "Content-Type": "application/json",
        },
      }
    );

    // 4. Send success response
    return response.data;
  } catch (error) {
    console.error(
      "Stakeholder creation error:",
      error.response?.data || error.message
    );
  }
}

async function enableRouteProduct(accountId) {
  const requestBody = {
    product_name: "route",
    tnc_accepted: true,
  };

  const response = await axios.post(
    `https://api.razorpay.com/v2/accounts/${accountId}/products`,
    requestBody,
    {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/json",
      },
    }
  );

  return response.data;
}

async function configureRazorpaySettlements({
  accountId,
  productId,
  account_number,
  ifsc_code,
  beneficiary_name,
}) {
  const requestBody = {
    settlements: {
      account_number,
      ifsc_code,
      beneficiary_name,
    },
    tnc_accepted: true,
  };

  const response = await axios.patch(
    `https://api.razorpay.com/v2/accounts/${accountId}/products/${productId}`,
    requestBody,
    {
      headers: {
        Authorization: `Basic ${auth}`,
        "Content-Type": "application/json",
      },
    }
  );

  return response.data;
}

function getValidCountryCode(input) {
  const mapping = {
    India: "IN",
    USA: "US",
    "United States": "US",
    "United Kingdom": "GB",
    UK: "GB",
    Canada: "CA",
    Australia: "AU",
    // add more if needed
  };

  if (!input) return null;

  const code = input.toUpperCase();
  if (code.length === 2) return code; // assume valid ISO code
  return mapping[input.trim()] || null;
}

async function createPlan(req, res) {
  try {
    const result = await splitPaymentService.createPlan(req.body);
    res.json(result);
  } catch (err) {
    console.error("Plan creation failed:", err);
    res.status(500).json({
      success: false,
      error: "Failed to create plan",
      details: err.message,
    });
  }
}

async function createCustomer(req, res) {
  try {
    const result = await splitPaymentService.createCustomer(req.body);
    res.json(result);
  } catch (err) {
    console.error("Customer creation error:", err);
    res.status(500).json({
      success: false,
      error: "Failed to create customer",
      details: err.message,
    });
  }
}

async function createSubscription(req, res) {
  try {
    const result = await splitPaymentService.createSubscription(req.body);
    res.json(result);
  } catch (err) {
    console.error("Subscription creation failed:", err);
    res.status(500).json({
      success: false,
      error: "Failed to create subscription",
      details: err.message,
    });
  }
}

/**
 * Complete Subscription Setup API
 *
 * This endpoint handles the complete subscription setup process in one call:
 * 1. Creates a Razorpay plan with the provided details
 * 2. Creates or retrieves an existing Razorpay customer based on email/contact
 * 3. Creates a subscription linking the plan and customer
 *
 * Required Parameters:
 * - planName: Name for the subscription plan
 * - amount: Amount in paise (e.g., 50000 for ₹500)
 * - description: Description of the plan
 * - name: Customer name
 * - email: Customer email (must match existing user)
 * - contact: Customer mobile number (must match existing user)
 *
 * Optional Parameters:
 * - period: "daily", "weekly", "monthly", "yearly" (default: "monthly")
 * - interval: Number of periods (default: 1)
 * - currency: Currency code (default: "INR")
 * - total_count: Total billing cycles (default: 12)
 * - quantity: Subscription quantity (default: 1)
 * - notes: Additional notes object (default: {})
 *
 * Response:
 * - success: Boolean indicating success
 * - message: Success message
 * - data: Object containing plan, customer, subscription details
 * - data.isExistingCustomer: Boolean indicating if customer already existed
 */
async function setupSubscription(req, res) {
  try {
    const result = await splitPaymentService.setupSubscription(req.body);
    res.json(result);
  } catch (err) {
    console.error("Setup subscription error:", err);
    res.status(500).json({
      success: false,
      error: "Failed to setup subscription",
      details: err.message,
    });
  }
}

// Pause
async function pauseSubscription(req, res) {
  const { subscription_id, pause_at = "now" } = req.body;
  try {
    const result = await splitPaymentService.pauseSubscription(
      subscription_id,
      pause_at
    );
    res.json(result);
  } catch (err) {
    res.status(500).json({
      success: false,
      error: "Failed to pause subscription",
      details: err.message,
    });
  }
}

// Resume
async function resumeSubscription(req, res) {
  const { subscription_id, resume_at = "now" } = req.body;
  try {
    const result = await splitPaymentService.resumeSubscription(
      subscription_id,
      resume_at
    );
    res.json(result);
  } catch (err) {
    res.status(500).json({
      success: false,
      error: "Failed to resume subscription",
      details: err.message,
    });
  }
}

// Cancel
async function cancelSubscription(req, res) {
  const { subscription_id, cancel_at_cycle_end = false } = req.body;
  try {
    const result = await splitPaymentService.cancelSubscription(
      subscription_id,
      cancel_at_cycle_end
    );
    res.json(result);
  } catch (err) {
    res.status(500).json({
      success: false,
      error: "Failed to cancel subscription",
      details: err.message,
    });
  }
}

// Webhook endpoint (ensure to validate signature)
// router.post(
//   "/razorpayWebhook",
//   express.raw({ type: "application/json" }),
//   async (req, res) => {
//     const signature = req.headers["x-razorpay-signature"];
//     const secret = config.RAZORPAY_WEBHOOK_SECRET;

//     const body = req.body;

//     const expected = crypto
//       .createHmac("sha256", secret)
//       .update(JSON.stringify(body))
//       .digest("hex");

//     if (signature === expected) {
//       const event = body.event;
//       const payload = body.payload;

//       switch (event) {
//         case "subscription.activated":
//           // Save subscription info to user record
//           break;
//         case "subscription.cancelled":
//           // Update subscription status in DB
//           break;
//         // Handle other events
//       }

//       res.status(200).send("Webhook received");
//     } else {
//       res.status(400).send("Invalid signature");
//     }
//   }
// );
router.post(
  "/razorpayWebhook",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const signature = req.headers["x-razorpay-signature"];
    const secret = config.RAZORPAY_WEBHOOK_SECRET;

    const rawBody = req.body;
    const expected = crypto
      .createHmac("sha256", secret)
      .update(rawBody)
      .digest("hex");

    if (signature === expected) {
      const parsedBody = JSON.parse(rawBody);
      const event = parsedBody.event;
      const payload = parsedBody.payload;

      try {
        // Use the service to handle webhook events
        const result = await splitPaymentService.handleWebhookEvent(
          event,
          payload
        );

        console.log("Webhook processed successfully:", result.message);
        res.status(200).json({
          success: true,
          message: result.message,
          event: event,
        });
      } catch (err) {
        console.error("Webhook handler error:", err);
        res.status(500).json({
          success: false,
          error: "Error processing webhook",
          details: err.message,
          event: event,
        });
      }
    } else {
      console.warn("Invalid signature");
      res.status(400).send("Invalid signature");
    }
  }
);

async function getSubscriptionStatus(req, res) {
  try {
    const { id } = req.params; // subscriptionId from URL
    if (!id) {
      return res.status(400).json({ error: "Subscription ID is required" });
    }

    const result = await splitPaymentService.getSubscriptionStatus(id);
    return res.json(result);
  } catch (error) {
    console.error("Get subscription status error:", error);
    return res.status(500).json({
      success: false,
      error: "Failed to fetch subscription status",
      details: error.message,
    });
  }
}
