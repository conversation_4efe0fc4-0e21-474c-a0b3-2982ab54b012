﻿const express = require("express");
const router = express.Router();
const Joi = require("joi");
const validateRequest = require("../_middleware/validate-request");
// const authorize = require("../_middleware/authorize");
const bankDetailService = require("./bank-detail.service");
const { logAction } = require("../_helpers/logger");
const axios = require("axios");

// Common Logging Function
function logRequest(req, action, type) {
  const userId = req?.headers?.userid;
  const pageName = req.query.pageName || req.body.pageName || "Unknown Page";
  logAction(userId, action, pageName, type);
}

// Routes
router.get("/", getAll);
router.post("/", updateSchema, create);
router.post("/razorpay-status", checkRazorpayStatus);
router.get("/:id", getById);
router.put("/:id", updateSchema, update);
router.delete("/:id", _delete);
router.get("/ifsc-details/:ifsc", getIfscDetails);

module.exports = router;

function create(req, res, next) {
  bankDetailService
    .create(req.body)
    .then((record) => {
      logRequest(
        req,
        `Created a new bank detail entry for ${record?.beneficiary_name}`,
        "CREATE"
      );
      res.json({
        status: true,
        message: "Record created successfully",
        data: record,
      });
    })
    .catch(next);
}

function getAll(req, res, next) {
  bankDetailService
    .getAll(req.query, req?.headers?.isadmin)
    .then((records) => {
      logRequest(req, "Fetched all bank details", "READ");
      res.json(records);
    })
    .catch(next);
}

function getById(req, res, next) {
  bankDetailService
    .getById(req.params.id)
    .then((record) => {
      logRequest(
        req,
        `Fetched bank detail for ${record?.beneficiary_name}`,
        "READ"
      );
      res.json(record);
    })
    .catch(next);
}

function updateSchema(req, res, next) {
  const schema = Joi.object({
    ngo_id: Joi.number().optional(),
    account_number: Joi.string().required(),
    status: Joi.string().allow(null, ""),
    ifsc_code: Joi.string().required(),
    beneficiary_name: Joi.string().required(),
    bank_name: Joi.string().required(),
    branch_name: Joi.string().required(),
    branch_address: Joi.string().required(),
  });
  validateRequest(req, next, schema);
}

function update(req, res, next) {
  bankDetailService
    .update(req.params.id, req.body)
    .then((record) => {
      logRequest(
        req,
        `Updated bank detail for ${record?.beneficiary_name}`,
        "UPDATE"
      );
      res.json(record);
    })
    .catch(next);
}

function _delete(req, res, next) {
  bankDetailService
    .delete(req.params.id)
    .then(() => {
      logRequest(
        req,
        `Deleted bank detail with ID: ${req.params.id}`,
        "DELETE"
      );
      res.json({ status: true, message: "Record deleted successfully" });
    })
    .catch(next);
}

function checkRazorpayStatus(req, res, next) {
  bankDetailService
    .getRazorpayAccountStatusByNgoId(req.body)
    .then((result) => {
      logRequest(req, `Checked Razorpay account status`, "READ");
      res.json({
        status: true,
        activation_status: result.activation_status,
        isActivated: result.isActivated,
      });
    })
    .catch(next);
}

async function getIfscDetails(req, res, next) {
  const { ifsc } = req.params;
  if (!ifsc) {
    return res
      .status(400)
      .json({ status: false, message: "IFSC code is required" });
  }
  try {
    const response = await axios.get(`https://ifsc.razorpay.com/${ifsc}`);
    res.json({ status: true, data: response.data });
  } catch (error) {
    res.status(500).json({
      status: false,
      message: "Failed to fetch IFSC details",
      error: error.response?.data || error.message,
    });
  }
}
